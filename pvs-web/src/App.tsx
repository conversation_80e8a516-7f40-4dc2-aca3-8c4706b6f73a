import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import TrelloBoardPage from './pages/TrelloBoard';
import ProjectPage from './pages/Projects';
import ProductsPage from './pages/Products';
import ProductDetailPage from './pages/ProductDetail';
import ProductTimelinePage from './pages/ProductTimeline';
import ProductStatsPage from './pages/ProductStats';
import ProductJourneyPage from './pages/ProductJourney';
import ProductJourneyFormPage from './pages/ProductJourneyForm';
import ResourcesPage from './pages/Resources';
import ResourceStatsPage from './pages/ResourceStats';
import AdminComponentsPage from './pages/AdminComponents';
import AdminUsersPage from './pages/AdminUsers';
import AdminParametersPage from './pages/AdminParameters';
import LayoutTest from './pages/LayoutTest';
import Layout from './components/Layout';
import { useAuth } from './hooks/useAuth';

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return isAuthenticated ? <>{children}</> : <Navigate to="/login" />;
};

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Layout>
                <Dashboard />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/trello"
          element={
            <ProtectedRoute>
              <Layout>
                <TrelloBoardPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/projects"
          element={
            <ProtectedRoute>
              <Layout>
                <ProjectPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        {/* Products and Services Routes */}
        <Route
          path="/products"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductsPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/:id"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductDetailPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/:id/edit"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductDetailPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/journey"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductJourneyPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/journey/create"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductJourneyFormPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/journey/:id/edit"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductJourneyFormPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/timeline"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductTimelinePage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/stats"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductStatsPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        {/* Resources Routes */}
        <Route
          path="/resources"
          element={
            <ProtectedRoute>
              <Layout>
                <ResourcesPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/resources/stats"
          element={
            <ProtectedRoute>
              <Layout>
                <ResourceStatsPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        {/* Admin Routes */}
        <Route
          path="/admin/components"
          element={
            <ProtectedRoute>
              <Layout>
                <AdminComponentsPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/admin/users"
          element={
            <ProtectedRoute>
              <Layout>
                <AdminUsersPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/admin/parameters"
          element={
            <ProtectedRoute>
              <Layout>
                <AdminParametersPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/layout-test"
          element={
            <ProtectedRoute>
              <Layout>
                <LayoutTest />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route path="/" element={<Navigate to="/dashboard" />} />
      </Routes>
    </Router>
  );
};

export default App;

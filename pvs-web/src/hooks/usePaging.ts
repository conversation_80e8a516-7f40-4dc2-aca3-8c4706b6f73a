import { useState, useEffect, useCallback } from "react";

export interface PagingState {
    page: number;
    limit: number;
    total: number;
}

export interface PagingOptions {
    initialPage?: number;
    initialLimit?: number;
    resetOnLimitChange?: boolean;
}

export interface PagingResult {
    // State
    currentPage: number;
    limit: number;
    total: number;
    totalPages: number;

    // Computed values
    startItem: number;
    endItem: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    isEmpty: boolean;

    // Actions
    setPage: (page: number) => void;
    setLimit: (limit: number) => void;
    setTotal: (total: number) => void;
    nextPage: () => void;
    prevPage: () => void;
    firstPage: () => void;
    lastPage: () => void;
    reset: () => void;

    // For API calls
    getPaginationParams: () => { page: number; limit: number };

    // For components
    getPagingComponentProps: () => {
        currentPage: number;
        totalPages: number;
        onPageChange: (page: number) => void;
    };

    getPagingInfoProps: () => {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
    };
}

export const usePaging = (
    options: PagingOptions = {}
): PagingResult => {
    const {
        initialPage = 1,
        initialLimit = 10,
        resetOnLimitChange = true
    } = options;

    const [currentPage, setCurrentPage] = useState(initialPage);
    const [limit, setLimitState] = useState(initialLimit);
    const [total, setTotal] = useState(0);

    // Computed values
    const totalPages = Math.ceil(total / limit);
    const startItem = total === 0 ? 0 : (currentPage - 1) * limit + 1;
    const endItem = Math.min(currentPage * limit, total);
    const hasNextPage = currentPage < totalPages;
    const hasPrevPage = currentPage > 1;
    const isEmpty = total === 0;

    // Validate and set page
    const setPage = useCallback((page: number) => {
        if (page < 1) {
            setCurrentPage(1);
        } else if (page > totalPages && totalPages > 0) {
            setCurrentPage(totalPages);
        } else {
            setCurrentPage(page);
        }
    }, [totalPages]);

    // Set limit with optional page reset
    const setLimit = useCallback((newLimit: number) => {
        if (newLimit > 0) {
            setLimitState(newLimit);
            if (resetOnLimitChange) {
                setCurrentPage(1);
            }
        }
    }, [resetOnLimitChange]);

    // Navigation functions
    const nextPage = useCallback(() => {
        if (hasNextPage) {
            setPage(currentPage + 1);
        }
    }, [currentPage, hasNextPage, setPage]);

    const prevPage = useCallback(() => {
        if (hasPrevPage) {
            setPage(currentPage - 1);
        }
    }, [currentPage, hasPrevPage, setPage]);

    const firstPage = useCallback(() => {
        setPage(1);
    }, [setPage]);

    const lastPage = useCallback(() => {
        if (totalPages > 0) {
            setPage(totalPages);
        }
    }, [totalPages, setPage]);

    const reset = useCallback(() => {
        setCurrentPage(initialPage);
        setLimitState(initialLimit);
        setTotal(0);
    }, [initialPage, initialLimit]);

    // Helper functions for components
    const getPaginationParams = useCallback(() => ({
        page: currentPage,
        limit
    }), [currentPage, limit]);

    const getPagingComponentProps = useCallback(() => ({
        currentPage,
        totalPages,
        onPageChange: setPage
    }), [currentPage, totalPages, setPage]);

    const getPagingInfoProps = useCallback(() => ({
        currentPage,
        totalPages,
        totalItems: total,
        itemsPerPage: limit
    }), [currentPage, totalPages, total, limit]);

    // Auto-adjust page when total changes
    useEffect(() => {
        if (totalPages > 0 && currentPage > totalPages) {
            setCurrentPage(totalPages);
        }
    }, [totalPages, currentPage]);

    return {
        // State
        currentPage,
        limit,
        total,
        totalPages,

        // Computed values
        startItem,
        endItem,
        hasNextPage,
        hasPrevPage,
        isEmpty,

        // Actions
        setPage,
        setLimit,
        setTotal,
        nextPage,
        prevPage,
        firstPage,
        lastPage,
        reset,

        // Helper functions
        getPaginationParams,
        getPagingComponentProps,
        getPagingInfoProps
    };
};

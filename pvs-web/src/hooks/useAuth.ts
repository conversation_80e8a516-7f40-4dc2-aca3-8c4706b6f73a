// src/hooks/useAuth.ts

import { useState, useEffect } from 'react';
import { authService, type LoginRequest, type UserInfo } from '../services/authService';

interface AuthState {
    token: string | null;
    isAuthenticated: boolean;
    user: UserInfo | null;
    loading: boolean;
    error: string | null;
}

export const useAuth = () => {
    const [state, setState] = useState<AuthState>({
        token: localStorage.getItem('token'),
        isAuthenticated: <PERSON><PERSON><PERSON>(localStorage.getItem('token')),
        user: localStorage.getItem('user')
            ? JSON.parse(localStorage.getItem('user')!)
            : null,
        loading: false,
        error: null,
    });

    // Check if token exists on mount
    useEffect(() => {
        const token = localStorage.getItem('token');
        const user = localStorage.getItem('user');

        if (token && user) {
            setState(prev => ({
                ...prev,
                token,
                user: JSON.parse(user),
                isAuthenticated: true,
            }));
        }
    }, []);

    const login = async (credentials: LoginRequest): Promise<boolean> => {
        setState(prev => ({ ...prev, loading: true, error: null }));

        try {
            const response = await authService.login(credentials.username, credentials.password);

            // Save to localStorage
            localStorage.setItem('token', response.token);
            localStorage.setItem('user', JSON.stringify(response.user));

            // Update state
            setState({
                token: response.token,
                isAuthenticated: true,
                user: response.user,
                loading: false,
                error: null,
            });

            return true;
        } catch (error: any) {
            const errorMessage = error.message || 'Login failed';
            setState(prev => ({
                ...prev,
                loading: false,
                error: errorMessage,
            }));
            console.error('Login error:', error);
            return false;
        }
    };

    const logout = () => {
        // Clear localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Update state
        setState({
            token: null,
            isAuthenticated: false,
            user: null,
            loading: false,
            error: null,
        });
    };

    const clearError = () => {
        setState(prev => ({ ...prev, error: null }));
    };

    const getAuthHeader = () => {
        return state.token ? { Authorization: `Bearer ${state.token}` } : {};
    };

    return {
        ...state,
        login,
        logout,
        clearError,
        getAuthHeader,
    };
};
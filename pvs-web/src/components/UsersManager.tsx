import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Key, UserCheck, UserX, Filter, Users } from 'lucide-react';
import { userService, roleService } from '../services';
import type { User, CreateUserRequest, UpdateUserRequest, ChangePasswordRequest, UpdateUserRoleRequest, UserFilters, Role } from '../services';
import UserModal from './UserModal';
import ChangePasswordModal from './ChangePasswordModal';
import PagingComponent from './PagingComponent';
import PagingInfo from './PagingInfo';
import { usePaging } from '../hooks';
import { ConfirmDialog } from './ui';

interface UsersManagerProps {
  className?: string;
}

const UsersManager: React.FC<UsersManagerProps> = ({ className = '' }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<boolean | undefined>(undefined);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showToggleStatusModal, setShowToggleStatusModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [changingPasswordUser, setChangingPasswordUser] = useState<User | null>(null);
  const [toggleStatusUser, setToggleStatusUser] = useState<User | null>(null);
  const [deletingUser, setDeletingUser] = useState<User | null>(null);
  const [roles, setRoles] = useState<Role[]>([]);

  // Use paging hook
  const paging = usePaging({ initialLimit: 10 });

  // Load users and roles
  useEffect(() => {
    loadUsers();
    loadRoles();
  }, [paging.currentPage, selectedRole, selectedStatus]);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (paging.currentPage === 1) {
        loadUsers();
      } else {
        paging.firstPage();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const loadUsers = async () => {
    setLoading(true);
    try {
      const filters: UserFilters = {
        ...paging.getPaginationParams(),
        search: searchTerm || undefined,
        role_code: selectedRole || undefined,
        is_active: selectedStatus
      };

      const response = await userService.getUsers(filters);
      setUsers(response.data);
      paging.setTotal(response.total);
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRoles = async () => {
    try {
      const response = await roleService.getRoles({ limit: 100 });
      setRoles(response.data);
    } catch (error) {
      console.error('Error loading roles:', error);
    }
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    setShowUserModal(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setShowUserModal(true);
  };

  const handleCloseUserModal = () => {
    setShowUserModal(false);
    setEditingUser(null);
  };

  const handleSaveUser = async (data: CreateUserRequest | UpdateUserRequest) => {
    if (editingUser) {
      // Update existing user
      await userService.updateUser(editingUser.id, data as UpdateUserRequest);
    } else {
      // Create new user
      await userService.createUser(data as CreateUserRequest);
    }
    await loadUsers();
  };

  const handleChangePassword = (user: User) => {
    setChangingPasswordUser(user);
    setShowPasswordModal(true);
  };

  const handleClosePasswordModal = () => {
    setShowPasswordModal(false);
    setChangingPasswordUser(null);
  };

  const handleSavePassword = async (data: ChangePasswordRequest) => {
    if (changingPasswordUser) {
      await userService.changePassword(changingPasswordUser.id, data);
    }
  };

  const handleToggleUserStatus = (user: User) => {
    setToggleStatusUser(user);
    setShowToggleStatusModal(true);
  };

  const handleConfirmToggleStatus = async () => {
    if (!toggleStatusUser) return;

    try {
      await userService.toggleUserStatus(toggleStatusUser.id, !toggleStatusUser.is_active);
      await loadUsers();
      setShowToggleStatusModal(false);
      setToggleStatusUser(null);
    } catch (error) {
      console.error('Error toggling user status:', error);
    }
  };

  const handleDeleteUser = (user: User) => {
    setDeletingUser(user);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!deletingUser) return;

    try {
      await userService.deleteUser(deletingUser.id);
      await loadUsers();
      setShowDeleteModal(false);
      setDeletingUser(null);
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  };

  const getRoleName = (roleCode: string) => {
    const role = roles.find(r => r.code === roleCode);
    return role ? role.name : roleCode;
  };

  const getRoleBadgeClass = (roleCode: string) => {
    switch (roleCode.toLowerCase()) {
      case 'admin':
        return 'badge-error';
      case 'manager':
        return 'badge-warning';
      case 'user':
        return 'badge-primary';
      default:
        return 'badge-secondary';
    }
  };

  const getUserAvatar = (fullName: string) => {
    const initials = fullName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    return (
      <div className="w-10 h-10 rounded-full bg-primary-600 text-white flex items-center justify-center mr-3 text-sm font-bold">
        {initials}
      </div>
    );
  };

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Danh sách người dùng</h2>
          <p className="text-gray-500 text-sm mt-1">Quản lý tài khoản và quyền hạn người dùng</p>
        </div>
        <button
          type="button"
          className="btn btn-primary flex items-center"
          onClick={handleCreateUser}
        >
          <Plus size={16} className="mr-2" />
          Thêm người dùng
        </button>
      </div>

      {/* Search and Filters */}
      <div className="card mb-6">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tìm kiếm người dùng
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  placeholder="Tìm kiếm người dùng..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Role Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Vai trò
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm"
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
              >
                <option value="">Tất cả vai trò</option>
                {roles.map((role) => (
                  <option key={role.code} value={role.code}>
                    {role.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trạng thái
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm"
                value={selectedStatus === undefined ? '' : selectedStatus.toString()}
                onChange={(e) => {
                  const value = e.target.value;
                  setSelectedStatus(value === '' ? undefined : value === 'true');
                }}
              >
                <option value="">Tất cả trạng thái</option>
                <option value="true">Hoạt động</option>
                <option value="false">Không hoạt động</option>
              </select>
            </div>

            {/* Clear Filters Button */}
            <div className="flex items-end">
              <button
                type="button"
                className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 flex items-center justify-center"
                onClick={() => {
                  setSearchTerm('');
                  setSelectedRole('');
                  setSelectedStatus(undefined);
                }}
                title="Xóa bộ lọc"
              >
                <Filter size={16} className="mr-2" />
                Xóa bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="card">
        <div className="p-0">
          {loading ? (
            <div className="text-center py-12">
              <div className="spinner w-8 h-8 mx-auto" role="status">
                <span className="sr-only">Loading...</span>
              </div>
              <p className="mt-3 text-gray-600">Đang tải dữ liệu...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-header-cell">Người dùng</th>
                    <th className="table-header-cell">Vai trò</th>
                    <th className="table-header-cell">Trạng thái</th>
                    <th className="table-header-cell">Đăng nhập cuối</th>
                    <th className="table-header-cell">Thao tác</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {users.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="table-cell text-center py-12 text-gray-500">
                        <div className="flex flex-col items-center">
                          <Users size={48} className="mb-3 opacity-50" />
                          <h5 className="text-lg font-medium text-gray-700 mb-2">
                            {searchTerm || selectedRole || selectedStatus !== undefined
                              ? 'Không tìm thấy người dùng nào'
                              : 'Chưa có người dùng nào'}
                          </h5>
                          <p className="text-gray-500">
                            {searchTerm || selectedRole || selectedStatus !== undefined
                              ? 'Thử thay đổi bộ lọc để xem thêm kết quả'
                              : 'Bắt đầu bằng cách thêm người dùng đầu tiên'}
                          </p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    users.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="table-cell">
                          <div className="flex items-center">
                            {getUserAvatar(user.full_name)}
                            <div>
                              <div className="font-semibold text-gray-900">{user.full_name}</div>
                              <div className="text-sm text-gray-500">@{user.username}</div>
                            </div>
                          </div>
                        </td>
                        <td className="table-cell">
                          <span className={`badge ${getRoleBadgeClass(user.role_code)}`}>
                            {getRoleName(user.role_code)}
                          </span>
                        </td>
                        <td className="table-cell">
                          <button
                            type="button"
                            className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-colors duration-200 ${
                              user.is_active
                                ? 'bg-success-100 text-success-800 hover:bg-success-200'
                                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                            }`}
                            onClick={() => handleToggleUserStatus(user)}
                            title={user.is_active ? 'Đang hoạt động - Click để tạm dừng' : 'Không hoạt động - Click để kích hoạt'}
                          >
                            {user.is_active ? (
                              <>
                                <UserCheck size={12} className="mr-1" />
                                Hoạt động
                              </>
                            ) : (
                              <>
                                <UserX size={12} className="mr-1" />
                                Tạm dừng
                              </>
                            )}
                          </button>
                        </td>
                        <td className="table-cell">
                          <span className="text-sm text-gray-500">
                            {user.last_login
                              ? new Date(user.last_login).toLocaleDateString('vi-VN')
                              : 'Chưa đăng nhập'}
                          </span>
                        </td>
                        <td className="table-cell">
                          <div className="flex gap-2">
                            <button
                              type="button"
                              className="inline-flex items-center justify-center w-8 h-8 text-primary-600 bg-primary-50 border border-primary-200 rounded-lg hover:bg-primary-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                              onClick={() => handleEditUser(user)}
                              title="Chỉnh sửa"
                            >
                              <Edit size={14} />
                            </button>
                            <button
                              type="button"
                              className="inline-flex items-center justify-center w-8 h-8 text-warning-600 bg-warning-50 border border-warning-200 rounded-lg hover:bg-warning-100 focus:ring-2 focus:ring-warning-500 focus:border-warning-500 transition-colors duration-200"
                              onClick={() => handleChangePassword(user)}
                              title="Đổi mật khẩu"
                            >
                              <Key size={14} />
                            </button>
                            <button
                              type="button"
                              className="inline-flex items-center justify-center w-8 h-8 text-error-600 bg-error-50 border border-error-200 rounded-lg hover:bg-error-100 focus:ring-2 focus:ring-error-500 focus:border-error-500 transition-colors duration-200"
                              onClick={() => handleDeleteUser(user)}
                              title="Xóa"
                            >
                              <Trash2 size={14} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        {!loading && users.length > 0 && (
          <div className="flex justify-between items-center px-6 py-4 border-t border-gray-200">
            <PagingInfo {...paging.getPagingInfoProps()} />
            <PagingComponent
              {...paging.getPagingComponentProps()}
              maxVisiblePages={5}
              size="md"
            />
          </div>
        )}
      </div>

      {/* Modals */}
      <UserModal
        isOpen={showUserModal}
        onClose={handleCloseUserModal}
        onSave={handleSaveUser}
        user={editingUser}
        title={editingUser ? 'Chỉnh sửa người dùng' : 'Thêm người dùng mới'}
      />

      <ChangePasswordModal
        isOpen={showPasswordModal}
        onClose={handleClosePasswordModal}
        onSave={handleSavePassword}
        user={changingPasswordUser}
      />

      {/* Toggle Status Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showToggleStatusModal}
        onClose={() => {
          setShowToggleStatusModal(false);
          setToggleStatusUser(null);
        }}
        onConfirm={handleConfirmToggleStatus}
        title={`Xác nhận ${toggleStatusUser?.is_active ? 'vô hiệu hóa' : 'kích hoạt'} người dùng`}
        message={`Bạn có chắc chắn muốn ${toggleStatusUser?.is_active ? 'vô hiệu hóa' : 'kích hoạt'} người dùng "${toggleStatusUser?.full_name || ''}"?`}
        confirmText={toggleStatusUser?.is_active ? 'Vô hiệu hóa' : 'Kích hoạt'}
        cancelText="Hủy"
        variant="warning"
      />

      {/* Delete User Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingUser(null);
        }}
        onConfirm={handleConfirmDelete}
        title="Xác nhận xóa người dùng"
        message={`Bạn có chắc chắn muốn xóa người dùng "${deletingUser?.full_name || ''}"? Hành động này không thể hoàn tác.`}
        confirmText="Xóa"
        cancelText="Hủy"
        variant="error"
      />
    </div>
  );
};

export default UsersManager;
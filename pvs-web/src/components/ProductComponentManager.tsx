import React, { useState, useEffect } from 'react';
import { Plus, Trash2 } from 'lucide-react';
import { productService, componentService, type ProductComponent } from '../services';
import type { Component } from '../services/componentService';

interface ProductComponentManagerProps {
  productId: number;
  components: ProductComponent[];
  onComponentsChange: () => void;
  readOnly?: boolean;
}

const ProductComponentManager: React.FC<ProductComponentManagerProps> = ({
  productId,
  components,
  onComponentsChange,
  readOnly = false
}) => {
  const [availableComponents, setAvailableComponents] = useState<Component[]>([]);
  const [showAddComponent, setShowAddComponent] = useState(false);
  const [selectedComponentId, setSelectedComponentId] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [loadingComponents, setLoadingComponents] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load available components for adding
  useEffect(() => {
    const loadComponents = async () => {
      try {
        setLoadingComponents(true);
        setError(null);
        const result = await componentService.getComponents({ page: 1, limit: 100 });
        console.log('result', result);
        setAvailableComponents(result.data || []);
      } catch (err) {
        console.error('Failed to load components:', err);
        setError('Không thể tải danh sách components');
        setAvailableComponents([]);
      } finally {
        setLoadingComponents(false);
      }
    };

    if (showAddComponent) {
      loadComponents();
    }
  }, [showAddComponent]);

  const handleAddComponent = async () => {
    if (!selectedComponentId) return;

    try {
      setLoading(true);
      setError(null);
      await productService.addComponentToProduct(productId, { component_id: selectedComponentId });

      setShowAddComponent(false);
      setSelectedComponentId(0);
      onComponentsChange();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add component');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveComponent = async (componentId: number) => {
    if (!confirm('Bạn có chắc chắn muốn xóa component này khỏi sản phẩm?')) return;

    try {
      setLoading(true);
      setError(null);
      await productService.removeComponentFromProduct(productId, componentId);
      onComponentsChange();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove component');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="card">
      <div className="card-header flex justify-between items-center">
        <h5 className="text-lg font-semibold mb-0">Components ({components.length})</h5>
        {!readOnly && (
          <button
            className="btn btn-sm border border-primary-300 text-primary-600-600 hover:bg-primary-50"
            onClick={() => setShowAddComponent(true)}
            disabled={loading}
          >
            <Plus size={16} />
          </button>
        )}
      </div>
      <div className="p-6">
        {error && (
          <div className="alert alert alert-error alert-dismissible fade show" role="alert">
            {error}
            <button
              type="button"
              className="btn btn-close"
              onClick={() => setError(null)}
            ></button>
          </div>
        )}

        {components.length > 0 ? (
          <div className="space-y-2 space-y-0">
            {components.map(component => (
              <div key={component.id} className="flex justify-between items-center py-3 border-b border-gray-200 flex justify-between items-start">
                <div className="flex-grow-1">
                  <h6 className="mb-1">{component.name}</h6>
                  <p className="mb-1 text-gray-500 small">{component.description}</p>
                  <div className="flex gap-2">
                    <small className="text-gray-500">
                      <strong>Type:</strong> {component.component_type_code}
                    </small>
                    <small className="text-gray-500">
                      <strong>Target:</strong> {component.target_user_code}
                    </small>
                  </div>
                </div>
                {!readOnly && (
                  <button
                    className="btn btn-sm border border-error-300 text-error-600 hover:bg-error-50"
                    onClick={() => handleRemoveComponent(component.id)}
                    title="Xóa component"
                    disabled={loading}
                  >
                    <Trash2 size={14} />
                  </button>
                )}
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">Chưa có component nào</p>
        )}
      </div>

      {/* Add Component Modal */}
      {showAddComponent && (
        <div className="modal show block" tabIndex={-1}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Thêm Component</h5>
                <button
                  type="button"
                  className="btn btn-close"
                  onClick={() => {
                    setShowAddComponent(false);
                    setError(null);
                    setSelectedComponentId(0);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                {error && (
                  <div className="alert alert alert-error" role="alert">
                    {error}
                  </div>
                )}
                <div className="mb-3">
                  <label htmlFor="componentSelect" className="block text-sm font-medium text-gray-700 mb-2">Chọn Component</label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    id="componentSelect"
                    value={selectedComponentId}
                    onChange={(e) => setSelectedComponentId(parseInt(e.target.value))}
                  >
                    <option value={0}>-- Chọn component --</option>
                    {loadingComponents ? (
                      <option disabled>Đang tải...</option>
                    ) : availableComponents && availableComponents.length > 0 ? (
                      availableComponents
                        .filter(comp => !components.some(pc => pc.id === comp.id))
                        .map(component => (
                          <option key={component.id} value={component.id}>
                            {component.name} ({component.component_type_code})
                          </option>
                        ))
                    ) : (
                      <option disabled>Không có component nào khả dụng</option>
                    )}
                  </select>
                </div>
                {selectedComponentId > 0 && availableComponents && (
                  <div className="mb-3">
                    {(() => {
                      const selectedComp = availableComponents.find(c => c.id === selectedComponentId);
                      return selectedComp ? (
                        <div className="card badge-light">
                          <div className="p-6">
                            <h6 className="text-lg font-semibold">{selectedComp.name}</h6>
                            <p className="text-gray-600 small">{selectedComp.description}</p>
                            <div className="flex gap-2">
                              <small className="text-gray-500">
                                <strong>Type:</strong> {selectedComp.component_type_code}
                              </small>
                              <small className="text-gray-500">
                                <strong>Target:</strong> {selectedComp.target_user_code}
                              </small>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="alert alert alert-warning">
                          Component không tìm thấy
                        </div>
                      );
                    })()}
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowAddComponent(false);
                    setError(null);
                    setSelectedComponentId(0);
                  }}
                  disabled={loading}
                >
                  Hủy
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleAddComponent}
                  disabled={!selectedComponentId || loading}
                >
                  {loading ? 'Đang thêm...' : 'Thêm'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductComponentManager;

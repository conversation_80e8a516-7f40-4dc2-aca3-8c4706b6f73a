import React, { useState } from 'react';
import { Plus, Clock } from 'lucide-react';
import { productService, type ProductStatusLog } from '../services';

interface ProductStatusLogManagerProps {
  productId: number;
  statusLogs: ProductStatusLog[];
  onStatusLogsChange: () => void;
  readOnly?: boolean;
}

const ProductStatusLogManager: React.FC<ProductStatusLogManagerProps> = ({
  productId,
  statusLogs,
  onStatusLogsChange,
  readOnly = false
}) => {
  const [showAddStatusLog, setShowAddStatusLog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statusLogForm, setStatusLogForm] = useState({
    status: '',
    changed_by: '',
    note: ''
  });

  const handleAddStatusLog = async () => {
    if (!statusLogForm.status || !statusLogForm.changed_by) return;

    try {
      setLoading(true);
      setError(null);
      await productService.addStatusLogToProduct(productId, statusLogForm);
      
      setShowAddStatusLog(false);
      setStatusLogForm({ status: '', changed_by: '', note: '' });
      onStatusLogsChange();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add status log');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-success';
      case 'INACTIVE':
        return 'bg-secondary';
      case 'DEVELOPMENT':
        return 'bg-warning';
      case 'MAINTENANCE':
        return 'bg-info';
      default:
        return 'bg-secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="card">
      <div className="card-header flex justify-between items-center">
        <h5 className="text-lg font-semibold mb-0 flex items-center">
          <Clock size={20} className="mr-2" />
          Lịch sử trạng thái ({statusLogs.length})
        </h5>
        {!readOnly && (
          <button
            className="btn btn-sm border border-primary-300 text-primary-600-600 hover:bg-primary-50"
            onClick={() => setShowAddStatusLog(true)}
            disabled={loading}
          >
            <Plus size={16} />
          </button>
        )}
      </div>
      <div className="p-6">
        {error && (
          <div className="alert alert alert-error alert-dismissible fade show" role="alert">
            {error}
            <button
              type="button"
              className="btn btn-close"
              onClick={() => setError(null)}
            ></button>
          </div>
        )}

        {statusLogs.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="table table-sm hover:bg-gray-50">
              <thead>
                <tr>
                  <th className='table-header-cell'>Trạng thái</th>
                  <th className='table-header-cell'>Người thay đổi</th>
                  <th className='table-header-cell'>Thời gian</th>
                  <th className='table-header-cell'>Ghi chú</th>
                </tr>
              </thead>
              <tbody>
                {statusLogs
                  .sort((a, b) => new Date(b.changed_at).getTime() - new Date(a.changed_at).getTime())
                  .map(log => (
                    <tr key={log.id}>
                      <td className='table-cell'>
                        <span className={`badge ${getStatusBadgeClass(log.status)}`}>
                          {log.status}
                        </span>
                      </td>
                      <td className='table-cell'>{log.changed_by}</td>
                      <td className='table-cell'>{formatDate(log.changed_at)}</td>
                      <td className='table-cell'>{log.description || '-'}</td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-4">
            <Clock size={48} className="text-gray-500 mb-3" />
            <p className="text-gray-500">Chưa có lịch sử trạng thái</p>
          </div>
        )}
      </div>

      {/* Add Status Log Modal */}
      {showAddStatusLog && (
        <div className="modal show block" tabIndex={-1}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Thêm Status Log</h5>
                <button
                  type="button"
                  className="btn btn-close"
                  onClick={() => setShowAddStatusLog(false)}
                ></button>
              </div>
              <div className="modal-body">
                <div className="mb-3">
                  <label htmlFor="statusSelect" className="block text-sm font-medium text-gray-700 mb-2">
                    Trạng thái <span className="text-error-600">*</span>
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    id="statusSelect"
                    value={statusLogForm.status}
                    onChange={(e) => setStatusLogForm(prev => ({ ...prev, status: e.target.value }))}
                  >
                    <option value="">-- Chọn trạng thái --</option>
                    <option value="ACTIVE">ACTIVE</option>
                    <option value="INACTIVE">INACTIVE</option>
                    <option value="DEVELOPMENT">DEVELOPMENT</option>
                    <option value="MAINTENANCE">MAINTENANCE</option>
                  </select>
                </div>
                <div className="mb-3">
                  <label htmlFor="changedBy" className="block text-sm font-medium text-gray-700 mb-2">
                    Người thay đổi <span className="text-error-600">*</span>
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    id="changedBy"
                    value={statusLogForm.changed_by}
                    onChange={(e) => setStatusLogForm(prev => ({ ...prev, changed_by: e.target.value }))}
                    placeholder="Nhập tên người thay đổi"
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="note" className="block text-sm font-medium text-gray-700 mb-2">Ghi chú</label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    id="note"
                    rows={3}
                    value={statusLogForm.note}
                    onChange={(e) => setStatusLogForm(prev => ({ ...prev, note: e.target.value }))}
                    placeholder="Nhập ghi chú về thay đổi trạng thái..."
                  />
                </div>
                {statusLogForm.status && (
                  <div className="mb-3">
                    <div className="card badge-light">
                      <div className="p-6">
                        <h6 className="text-lg font-semibold">Xem trước</h6>
                        <p className="mb-1">
                          <strong>Trạng thái:</strong>{' '}
                          <span className={`badge ${getStatusBadgeClass(statusLogForm.status)}`}>
                            {statusLogForm.status}
                          </span>
                        </p>
                        <p className="mb-1">
                          <strong>Người thay đổi:</strong> {statusLogForm.changed_by || 'Chưa nhập'}
                        </p>
                        <p className="mb-1">
                          <strong>Thời gian:</strong> {formatDate(new Date().toISOString())}
                        </p>
                        <p className="mb-0">
                          <strong>Ghi chú:</strong> {statusLogForm.note || 'Không có ghi chú'}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowAddStatusLog(false)}
                  disabled={loading}
                >
                  Hủy
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleAddStatusLog}
                  disabled={!statusLogForm.status || !statusLogForm.changed_by || loading}
                >
                  {loading ? 'Đang thêm...' : 'Thêm'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductStatusLogManager;

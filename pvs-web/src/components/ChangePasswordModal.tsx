import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle, Eye, EyeOff, Key } from 'lucide-react';
import type { User, ChangePasswordRequest } from '../services';

interface ChangePasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ChangePasswordRequest) => Promise<void>;
  user: User | null;
}

const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({
  isOpen,
  onClose,
  onSave,
  user
}) => {
  const [formData, setFormData] = useState<ChangePasswordRequest>({
    old_password: '',
    new_password: ''
  });
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({ old_password: '', new_password: '' });
      setConfirmPassword('');
      setErrors({});
      setShowOldPassword(false);
      setShowNewPassword(false);
      setShowConfirmPassword(false);
    }
  }, [isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.old_password.trim()) {
      newErrors.old_password = 'Mật khẩu cũ là bắt buộc';
    }

    if (!formData.new_password.trim()) {
      newErrors.new_password = 'Mật khẩu mới là bắt buộc';
    } else if (formData.new_password.length < 6) {
      newErrors.new_password = 'Mật khẩu mới phải có ít nhất 6 ký tự';
    }

    if (!confirmPassword.trim()) {
      newErrors.confirm_password = 'Xác nhận mật khẩu là bắt buộc';
    } else if (confirmPassword !== formData.new_password) {
      newErrors.confirm_password = 'Mật khẩu xác nhận không khớp';
    }

    if (formData.old_password === formData.new_password && formData.old_password.trim()) {
      newErrors.new_password = 'Mật khẩu mới phải khác mật khẩu cũ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error changing password:', error);
      // Handle specific error cases
      if (error instanceof Error && error.message.includes('Invalid old password')) {
        setErrors({ old_password: 'Mật khẩu cũ không đúng' });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ChangePasswordRequest | 'confirm_password', value: string) => {
    if (field === 'confirm_password') {
      setConfirmPassword(value);
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen || !user) return null;

  return (
    <div className="modal show block" tabIndex={-1} style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title flex items-center">
              <Key size={20} className="mr-2" />
              Đổi mật khẩu
            </h5>
            <button
              type="button"
              className="btn btn-close"
              onClick={onClose}
              disabled={loading}
            ></button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              <div className="alert alert-info">
                <strong>Đổi mật khẩu cho:</strong> {user.full_name} (@{user.username})
              </div>

              <div className="mb-3">
                <label htmlFor="old_password" className="block text-sm font-medium text-gray-700 mb-2">
                  Mật khẩu cũ <span className="text-error-600">*</span>
                </label>
                <div className="relative">
                  <input
                    type={showOldPassword ? 'text' : 'password'}
                    className={`w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                      errors.old_password ? 'border-error-300' : ''
                    }`}
                    id="old_password"
                    value={formData.old_password}
                    onChange={(e) => handleInputChange('old_password', e.target.value)}
                    placeholder="Nhập mật khẩu cũ"
                    disabled={loading}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowOldPassword(!showOldPassword)}
                    disabled={loading}
                  >
                    {showOldPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
                {errors.old_password && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.old_password}
                  </div>
                )}
              </div>

              <div className="mb-3">
                <label htmlFor="new_password" className="block text-sm font-medium text-gray-700 mb-2">
                  Mật khẩu mới <span className="text-error-600">*</span>
                </label>
                <div className="relative">
                  <input
                    type={showNewPassword ? 'text' : 'password'}
                    className={`w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                      errors.new_password ? 'border-error-300' : ''
                    }`}
                    id="new_password"
                    value={formData.new_password}
                    onChange={(e) => handleInputChange('new_password', e.target.value)}
                    placeholder="Nhập mật khẩu mới"
                    disabled={loading}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    disabled={loading}
                  >
                    {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
                {errors.new_password && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.new_password}
                  </div>
                )}
                <div className="text-xs text-gray-500 mt-1">
                  Mật khẩu phải có ít nhất 6 ký tự
                </div>
              </div>

              <div className="mb-3">
                <label htmlFor="confirm_password" className="block text-sm font-medium text-gray-700 mb-2">
                  Xác nhận mật khẩu mới <span className="text-error-600">*</span>
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    className={`w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                      errors.confirm_password ? 'border-error-300' : ''
                    }`}
                    id="confirm_password"
                    value={confirmPassword}
                    onChange={(e) => handleInputChange('confirm_password', e.target.value)}
                    placeholder="Nhập lại mật khẩu mới"
                    disabled={loading}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={loading}
                  >
                    {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
                {errors.confirm_password && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.confirm_password}
                  </div>
                )}
              </div>

              <div className="alert alert-warning">
                <strong>Lưu ý:</strong> Sau khi đổi mật khẩu, người dùng sẽ cần đăng nhập lại với mật khẩu mới.
              </div>
            </div>

            <div className="modal-footer">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={onClose}
                disabled={loading}
              >
                <X size={16} className="mr-1" />
                Hủy
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="spinner w-4 h-4 mr-2" role="status"></span>
                    Đang đổi...
                  </>
                ) : (
                  <>
                    <Save size={16} className="mr-1" />
                    Đổi mật khẩu
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChangePasswordModal;
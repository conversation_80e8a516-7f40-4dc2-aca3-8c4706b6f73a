import React, { useState, useRef, useEffect } from 'react';
import Sidebar from './Sidebar';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Badge } from './ui';
import {
  Menu,
  Bell,
  User,
  ChevronDown,
  Info,
  AlertTriangle,
  CheckCircle,
  Settings,
  LogOut,
  Search
} from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarMobileOpen, setSidebarMobileOpen] = useState(false);
  const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const notificationDropdownRef = useRef<HTMLDivElement>(null);
  const userDropdownRef = useRef<HTMLDivElement>(null);

  // Handle clicking outside dropdowns to close them
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target as Node)) {
        setNotificationDropdownOpen(false);
      }
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false);
      }
      // Close mobile sidebar when clicking outside
      if (sidebarMobileOpen && !(event.target as Element).closest('.sidebar')) {
        setSidebarMobileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [sidebarMobileOpen]);

  const handleSidebarToggle = () => {
    // Check if we're in mobile mode
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
      setSidebarMobileOpen(!sidebarMobileOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleNotificationDropdown = () => {
    setNotificationDropdownOpen(!notificationDropdownOpen);
    setUserDropdownOpen(false); // Close other dropdown
  };

  const toggleUserDropdown = () => {
    setUserDropdownOpen(!userDropdownOpen);
    setNotificationDropdownOpen(false); // Close other dropdown
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile overlay */}
      {sidebarMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setSidebarMobileOpen(false)}
        />
      )}

      <Sidebar
        isCollapsed={sidebarCollapsed}
        isMobileOpen={sidebarMobileOpen}
        onToggle={handleSidebarToggle}
        onMobileClose={() => setSidebarMobileOpen(false)}
      />

      <div
        className={`layout-main layout-sidebar-space ${
          sidebarCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'
        }`}
      >
        {/* Top Navigation Bar */}
        <header className="bg-white border-b border-gray-200 shadow-sm">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              {/* Left side */}
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className="md:hidden mr-2"
                  onClick={handleSidebarToggle}
                >
                  <Menu className="h-5 w-5" />
                </Button>

                {/* Search bar */}
                <div className="hidden md:block">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search..."
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-64"
                    />
                  </div>
                </div>
              </div>

              {/* Right side */}
              <div className="flex items-center space-x-4">
                {/* Notifications */}
                <div className="relative" ref={notificationDropdownRef}>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="relative p-2"
                    onClick={toggleNotificationDropdown}
                  >
                    <Bell className="h-5 w-5" />
                    <Badge
                      variant="error"
                      className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                    >
                      3
                    </Badge>
                  </Button>

                  {notificationDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                      <div className="p-4 border-b border-gray-200">
                        <h3 className="text-sm font-semibold text-gray-900">Notifications</h3>
                      </div>
                      <div className="max-h-64 overflow-y-auto">
                        <div className="p-2 space-y-1">
                          <a href="#" className="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                            <Info className="h-4 w-4 text-blue-500 mr-3" />
                            <span className="text-sm text-gray-700">New user registered</span>
                          </a>
                          <a href="#" className="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                            <AlertTriangle className="h-4 w-4 text-yellow-500 mr-3" />
                            <span className="text-sm text-gray-700">Server maintenance</span>
                          </a>
                          <a href="#" className="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-3" />
                            <span className="text-sm text-gray-700">Backup completed</span>
                          </a>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* User Menu */}
                <div className="relative" ref={userDropdownRef}>
                  <Button
                    variant="ghost"
                    className="flex items-center space-x-2 p-2"
                    onClick={toggleUserDropdown}
                  >
                    <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                      <User className="h-4 w-4 text-white" />
                    </div>
                    <span className="hidden sm:block text-sm font-medium text-gray-700">
                      {user?.username}
                    </span>
                    <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${userDropdownOpen ? 'rotate-180' : ''}`} />
                  </Button>

                  {userDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                      <div className="p-2">
                        <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                          Account
                        </div>
                        <a href="#" className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                          <User className="h-4 w-4 mr-3" />
                          Profile
                        </a>
                        <a href="#" className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                          <Settings className="h-4 w-4 mr-3" />
                          Settings
                        </a>
                        <hr className="my-2 border-gray-200" />
                        <button
                          type="button"
                          className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          onClick={handleLogout}
                        >
                          <LogOut className="h-4 w-4 mr-3" />
                          Logout
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
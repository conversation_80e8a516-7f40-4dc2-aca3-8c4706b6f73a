import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  ExternalLink,
  Layers,
  Filter
} from 'lucide-react';
import { componentService, systemParamService } from '../services';
import type { Component, ComponentRequest, ComponentFilters, SystemParam } from '../services';
import ComponentModal from './ComponentModal';
import PagingComponent from './PagingComponent';
import PagingInfo from './PagingInfo';
import { usePaging } from '../hooks';

interface ComponentsManagerProps {
  className?: string;
}

const ComponentsManager: React.FC<ComponentsManagerProps> = ({ className = '' }) => {
  const [components, setComponents] = useState<Component[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedComponentType, setSelectedComponentType] = useState('');
  const [selectedTargetUser, setSelectedTargetUser] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingComponent, setEditingComponent] = useState<Component | null>(null);
  const [componentTypes, setComponentTypes] = useState<SystemParam[]>([]);
  const [targetUsers, setTargetUsers] = useState<SystemParam[]>([]);

  // Use paging hook
  const paging = usePaging({ initialLimit: 10 });

  // Load components and dropdown options
  useEffect(() => {
    loadComponents();
  }, [paging.currentPage, selectedComponentType, selectedTargetUser]);

  useEffect(() => {
    loadDropdownOptions();
  }, []);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (paging.currentPage === 1) {
        loadComponents();
      } else {
        paging.firstPage();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const loadComponents = async () => {
    setLoading(true);
    try {
      const filters: ComponentFilters = {
        ...paging.getPaginationParams(),
        search: searchTerm || undefined,
        component_type_code: selectedComponentType || undefined,
        target_user_code: selectedTargetUser || undefined
      };

      const response = await componentService.getComponents(filters);
      setComponents(response.data);
      paging.setTotal(response.total);
    } catch (error) {
      console.error('Error loading components:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDropdownOptions = async () => {
    try {
      const [componentTypesRes, targetUsersRes] = await Promise.all([
        systemParamService.getSystemParamsByType('COMPONENT_TYPE'),
        systemParamService.getSystemParamsByType('TARGET_USER')
      ]);

      setComponentTypes(componentTypesRes.data.filter(item => item.is_active));
      setTargetUsers(targetUsersRes.data.filter(item => item.is_active));
    } catch (error) {
      console.error('Error loading dropdown options:', error);
    }
  };

  const handleCreate = () => {
    setEditingComponent(null);
    setShowModal(true);
  };

  const handleEdit = (component: Component) => {
    setEditingComponent(component);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingComponent(null);
  };

  const handleSaveComponent = async (data: ComponentRequest) => {
    if (editingComponent) {
      // Update existing component
      await componentService.updateComponent(editingComponent.id, data);
    } else {
      // Create new component
      await componentService.createComponent(data);
    }
    await loadComponents();
  };

  const handleDelete = async (component: Component) => {
    if (window.confirm(`Bạn có chắc chắn muốn xóa thành phần "${component.name}"?`)) {
      try {
        await componentService.deleteComponent(component.id);
        await loadComponents();
      } catch (error) {
        console.error('Error deleting component:', error);
      }
    }
  };

  const getComponentTypeName = (code: string) => {
    const type = componentTypes.find(t => t.code === code);
    return type ? type.name : code;
  };

  const getTargetUserName = (code: string) => {
    const user = targetUsers.find(u => u.code === code);
    return user ? user.name : code;
  };

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Danh sách thành phần</h2>
          <p className="text-gray-500 text-sm mt-1">Quản lý và cấu hình các thành phần hệ thống</p>
        </div>
        <button
          type="button"
          className="btn btn-primary flex items-center"
          onClick={handleCreate}
        >
          <Plus size={16} className="mr-2" />
          Thêm thành phần
        </button>
      </div>

      {/* Search and Filters */}
      <div className="card mb-6">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search Input */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tìm kiếm thành phần
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  placeholder="Tìm kiếm thành phần..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Component Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Loại thành phần
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm"
                value={selectedComponentType}
                onChange={(e) => setSelectedComponentType(e.target.value)}
              >
                <option value="">Tất cả loại</option>
                {componentTypes.map((type) => (
                  <option key={type.id} value={type.code}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Target User Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nhóm người dùng
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm"
                value={selectedTargetUser}
                onChange={(e) => setSelectedTargetUser(e.target.value)}
              >
                <option value="">Tất cả nhóm</option>
                {targetUsers.map((user) => (
                  <option key={user.id} value={user.code}>
                    {user.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Clear Filters Button */}
            <div className="flex items-end">
              <button
                type="button"
                className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 flex items-center justify-center"
                onClick={() => {
                  setSearchTerm('');
                  setSelectedComponentType('');
                  setSelectedTargetUser('');
                }}
                title="Xóa bộ lọc"
              >
                <Filter size={16} className="mr-2" />
                Xóa bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Components Table */}
      <div className="card">
        <div className="p-0">
          {loading ? (
            <div className="text-center py-12">
              <div className="spinner w-8 h-8 mx-auto" role="status">
                <span className="sr-only">Loading...</span>
              </div>
              <p className="mt-3 text-gray-600">Đang tải dữ liệu...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-header-cell">Tên thành phần</th>
                    <th className="table-header-cell">Loại</th>
                    <th className="table-header-cell">Nhóm người dùng</th>
                    <th className="table-header-cell">Chức năng chính</th>
                    <th className="table-header-cell">Thao tác</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {components.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="table-cell text-center py-12 text-gray-500">
                        <div className="flex flex-col items-center">
                          <Layers size={48} className="mb-3 opacity-50" />
                          <h5 className="text-lg font-medium text-gray-700 mb-2">
                            {searchTerm || selectedComponentType || selectedTargetUser
                              ? 'Không tìm thấy thành phần nào'
                              : 'Chưa có thành phần nào'}
                          </h5>
                          <p className="text-gray-500">
                            {searchTerm || selectedComponentType || selectedTargetUser
                              ? 'Thử thay đổi bộ lọc để xem thêm kết quả'
                              : 'Bắt đầu bằng cách thêm thành phần đầu tiên'}
                          </p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    components.map((component) => (
                      <tr key={component.id} className="hover:bg-gray-50">
                        <td className="table-cell">
                          <div className="flex items-center">
                            <div>
                              <div
                                className="inline-block px-2 py-1 rounded text-white text-sm font-medium mb-1"
                                style={{ backgroundColor: component.color_code || '#007bff' }}
                              >
                                {component.name}
                              </div>
                              {component.description && (
                                <div className="text-sm text-gray-500">
                                  {component.description.length > 50
                                    ? `${component.description.substring(0, 50)}...`
                                    : component.description}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="table-cell">
                          <span className="badge badge-primary">
                            {getComponentTypeName(component.component_type_code)}
                          </span>
                        </td>
                        <td className="table-cell">
                          <span className="badge badge-success">
                            {getTargetUserName(component.target_user_code)}
                          </span>
                        </td>
                        <td className="table-cell">
                          <span className="text-sm text-gray-500">
                            {component.main_function || '-'}
                          </span>
                        </td>
                        <td className="table-cell">
                          <div className="flex gap-2">
                            <button
                              type="button"
                              className="inline-flex items-center justify-center w-8 h-8 text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                              title="Xem chi tiết"
                            >
                              <Eye size={14} />
                            </button>
                            <button
                              type="button"
                              className="inline-flex items-center justify-center w-8 h-8 text-primary-600 bg-primary-50 border border-primary-200 rounded-lg hover:bg-primary-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                              onClick={() => handleEdit(component)}
                              title="Chỉnh sửa"
                            >
                              <Edit size={14} />
                            </button>
                            {component.confluence_url && (
                              <a
                                href={component.confluence_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center justify-center w-8 h-8 text-gray-600 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 focus:border-gray-500 transition-colors duration-200"
                                title="Mở Confluence"
                              >
                                <ExternalLink size={14} />
                              </a>
                            )}
                            <button
                              type="button"
                              className="inline-flex items-center justify-center w-8 h-8 text-error-600 bg-error-50 border border-error-200 rounded-lg hover:bg-error-100 focus:ring-2 focus:ring-error-500 focus:border-error-500 transition-colors duration-200"
                              onClick={() => handleDelete(component)}
                              title="Xóa"
                            >
                              <Trash2 size={14} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        {!loading && components.length > 0 && (
          <div className="flex justify-between items-center px-6 py-4 border-t border-gray-200">
            <PagingInfo
              {...paging.getPagingInfoProps()}
            />
            <PagingComponent
              {...paging.getPagingComponentProps()}
              maxVisiblePages={5}
              size="md"
            />
          </div>
        )}
      </div>

      {/* Modal for Create/Edit */}
      <ComponentModal
        isOpen={showModal}
        onClose={handleCloseModal}
        onSave={handleSaveComponent}
        component={editingComponent}
        title={editingComponent ? 'Chỉnh sửa thành phần' : 'Thêm thành phần mới'}
      />
    </div>
  );
};

export default ComponentsManager;

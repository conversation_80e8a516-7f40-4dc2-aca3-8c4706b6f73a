import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { roleService } from '../services';
import type { User, CreateUserRequest, UpdateUserRequest, Role } from '../services';

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: CreateUserRequest | UpdateUserRequest) => Promise<void>;
  user?: User | null;
  title: string;
}

const UserModal: React.FC<UserModalProps> = ({
  isOpen,
  onClose,
  onSave,
  user,
  title
}) => {
  const [formData, setFormData] = useState<CreateUserRequest>({
    username: '',
    password: '',
    full_name: '',
    role_code: ''
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [roles, setRoles] = useState<Role[]>([]);
  const [loadingRoles, setLoadingRoles] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Load roles
  useEffect(() => {
    if (isOpen) {
      loadRoles();
    }
  }, [isOpen]);

  // Reset form when modal opens/closes or user changes
  useEffect(() => {
    if (isOpen) {
      if (user) {
        // Edit mode - don't include password
        setFormData({
          username: user.username,
          password: '', // Password not needed for update
          full_name: user.full_name,
          role_code: user.role_code
        });
      } else {
        // Create mode
        setFormData({
          username: '',
          password: '',
          full_name: '',
          role_code: ''
        });
      }
      setErrors({});
      setShowPassword(false);
    }
  }, [isOpen, user]);

  const loadRoles = async () => {
    setLoadingRoles(true);
    try {
      const response = await roleService.getRoles({ limit: 100 });
      setRoles(response.data);
    } catch (error) {
      console.error('Error loading roles:', error);
    } finally {
      setLoadingRoles(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Tên đăng nhập là bắt buộc';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = 'Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới';
    }

    if (!user && !formData.password.trim()) {
      newErrors.password = 'Mật khẩu là bắt buộc';
    } else if (!user && formData.password.length < 6) {
      newErrors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
    }

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Họ tên là bắt buộc';
    }

    if (!formData.role_code) {
      newErrors.role_code = 'Vai trò là bắt buộc';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      if (user) {
        // Update mode - exclude username and password
        const updateData: UpdateUserRequest = {
          full_name: formData.full_name,
          is_active: user.is_active // Keep current status
        };
        await onSave(updateData);
      } else {
        // Create mode
        await onSave(formData);
      }
      onClose();
    } catch (error) {
      console.error('Error saving user:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof CreateUserRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal show block" tabIndex={-1} style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">{title}</h5>
            <button
              type="button"
              className="btn btn-close"
              onClick={onClose}
              disabled={loading}
            ></button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              {loadingRoles && (
                <div className="text-center mb-3">
                  <div className="spinner w-4 h-4 inline-block" role="status">
                    <span className="sr-only">Loading...</span>
                  </div>
                  <span className="ml-2">Đang tải dữ liệu...</span>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                    Tên đăng nhập <span className="text-error-600">*</span>
                  </label>
                  <input
                    type="text"
                    className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                      errors.username ? 'border-error-300' : ''
                    }`}
                    id="username"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    placeholder="VD: john_doe"
                    disabled={loading || !!user} // Disable when editing
                  />
                  {errors.username && (
                    <div className="text-error-600 text-sm mt-1 flex items-center">
                      <AlertCircle size={16} className="mr-1" />
                      {errors.username}
                    </div>
                  )}
                  {user && (
                    <div className="text-xs text-gray-500 mt-1">
                      Tên đăng nhập không thể thay đổi
                    </div>
                  )}
                </div>

                <div>
                  <label htmlFor="role_code" className="block text-sm font-medium text-gray-700 mb-2">
                    Vai trò <span className="text-error-600">*</span>
                  </label>
                  <select
                    className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                      errors.role_code ? 'border-error-300' : ''
                    }`}
                    id="role_code"
                    value={formData.role_code}
                    onChange={(e) => handleInputChange('role_code', e.target.value)}
                    disabled={loading || loadingRoles}
                  >
                    <option value="">Chọn vai trò</option>
                    {roles.map((role) => (
                      <option key={role.code} value={role.code}>
                        {role.name}
                      </option>
                    ))}
                  </select>
                  {errors.role_code && (
                    <div className="text-error-600 text-sm mt-1 flex items-center">
                      <AlertCircle size={16} className="mr-1" />
                      {errors.role_code}
                    </div>
                  )}
                </div>
              </div>

              <div className="mb-3">
                <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-2">
                  Họ tên <span className="text-error-600">*</span>
                </label>
                <input
                  type="text"
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.full_name ? 'border-error-300' : ''
                  }`}
                  id="full_name"
                  value={formData.full_name}
                  onChange={(e) => handleInputChange('full_name', e.target.value)}
                  placeholder="VD: Nguyễn Văn A"
                  disabled={loading}
                />
                {errors.full_name && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.full_name}
                  </div>
                )}
              </div>

              {!user && (
                <div className="mb-3">
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    Mật khẩu <span className="text-error-600">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      className={`w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                        errors.password ? 'border-error-300' : ''
                      }`}
                      id="password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      placeholder="Nhập mật khẩu"
                      disabled={loading}
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={loading}
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                  {errors.password && (
                    <div className="text-error-600 text-sm mt-1 flex items-center">
                      <AlertCircle size={16} className="mr-1" />
                      {errors.password}
                    </div>
                  )}
                  <div className="text-xs text-gray-500 mt-1">
                    Mật khẩu phải có ít nhất 6 ký tự
                  </div>
                </div>
              )}

              {user && (
                <div className="alert alert-info">
                  <strong>Lưu ý:</strong> Để thay đổi vai trò hoặc đổi mật khẩu, vui lòng sử dụng các chức năng riêng biệt.
                </div>
              )}
            </div>

            <div className="modal-footer">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={onClose}
                disabled={loading}
              >
                <X size={16} className="mr-1" />
                Hủy
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading || loadingRoles}
              >
                {loading ? (
                  <>
                    <span className="spinner w-4 h-4 mr-2" role="status"></span>
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <Save size={16} className="mr-1" />
                    {user ? 'Cập nhật' : 'Tạo mới'}
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserModal;
import React, { useState, useEffect, useRef } from 'react';
import {
  DndContext,
  DragOverlay,
  closestCorners,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragStartEvent,
  type DragEndEvent,
  type DragOverEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Plus, X, Edit3, Save } from 'lucide-react';
import { productService } from '../services';
import type { Component } from '../services';
import RecordButton from './ui/RecordButton';
import { ConfirmDialog } from './ui';

interface JourneyAction {
  id: string;
  content: string;
  stepId: string;
  description?: string;
  component_id?: number;
  component_name?: string;
}

interface JourneyStep {
  id: string;
  title: string;
  actionIds: string[];
  color?: string;
}

interface JourneyFlowData {
  steps: Record<string, JourneyStep>;
  actions: Record<string, JourneyAction>;
  stepOrder: string[];
}

interface JourneyFlowEditorProps {
  initialData?: string;
  productId: number;
  onChange: (data: string) => void;
  readOnly?: boolean;
}

// Default journey steps
const defaultSteps: Record<string, JourneyStep> = {
  'step1': { id: 'step1', title: 'Nhận diện', actionIds: [], color: '#e3f2fd' },
  'step2': { id: 'step2', title: 'Cân nhắc', actionIds: [], color: '#f3e5f5' },
  'step3': { id: 'step3', title: 'Đăng ký', actionIds: [], color: '#e8f5e8' },
  'step4': { id: 'step4', title: 'Sử dụng', actionIds: [], color: '#fff3e0' },
  'step5': { id: 'step5', title: 'Giữ chân', actionIds: [], color: '#fce4ec' },
};

const defaultStepOrder = ['step1', 'step2', 'step3', 'step4', 'step5'];

// Helper function to lighten color for inline styles
const lightenColor = (color: string, amount: number) => {
  if (!color || color === '') return 'rgba(0, 123, 255, 0.1)'; // Default blue

  const hex = color.replace("#", "");
  if (hex.length !== 6) return 'rgba(0, 123, 255, 0.1)'; // Default if invalid

  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${amount})`;
};

// Action Modal Component
function ActionModal({
  isOpen,
  onClose,
  onSave,
  action,
  components,
  isLoading
}: {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: { content: string; componentId: number }) => void;
  action?: JourneyAction | null;
  components: Component[];
  isLoading?: boolean;
}) {
  const [formData, setFormData] = useState({
    content: '',
    componentId: 0
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      if (action) {
        setFormData({
          content: action.content || '',
          componentId: action.component_id || 0
        });
      } else {
        setFormData({
          content: '',
          componentId: 0
        });
      }
      setErrors({});
    }
  }, [isOpen, action]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.content.trim()) {
      newErrors.content = 'Nội dung hành động là bắt buộc';
    }

    if (!formData.componentId) {
      newErrors.componentId = 'Vui lòng chọn component';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave(formData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose}></div>

        <div className="relative w-full max-w-md transform overflow-hidden rounded-lg bg-white shadow-xl transition-all">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <h3 className="text-lg font-semibold text-gray-900">
              {action ? 'Chỉnh sửa hành động' : 'Thêm hành động mới'}
            </h3>
            <button
              type="button"
              className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-1"
              onClick={onClose}
              disabled={isLoading}
            >
              <X size={20} />
            </button>
          </div>

          {/* Body */}
          <div className="px-6 py-4 space-y-4">
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                Nội dung hành động <span className="text-error-600">*</span>
              </label>
              <input
                type="text"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm ${
                  errors.content ? 'border-error-300 focus:ring-error-500 focus:border-error-500' : 'border-gray-300'
                }`}
                id="content"
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                placeholder="VD: Đăng nhập vào hệ thống"
                disabled={isLoading}
              />
              {errors.content && (
                <p className="mt-1 text-sm text-error-600">{errors.content}</p>
              )}
            </div>

            <div>
              <label htmlFor="componentId" className="block text-sm font-medium text-gray-700 mb-2">
                Component <span className="text-error-600">*</span>
              </label>
              <select
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm ${
                  errors.componentId ? 'border-error-300 focus:ring-error-500 focus:border-error-500' : 'border-gray-300'
                }`}
                id="componentId"
                value={formData.componentId}
                onChange={(e) => handleInputChange('componentId', parseInt(e.target.value))}
                disabled={isLoading}
              >
                <option value={0}>-- Chọn component --</option>
                {components.map(component => (
                  <option key={component.id} value={component.id}>
                    {component.name} ({component.component_type_code})
                  </option>
                ))}
              </select>
              {errors.componentId && (
                <p className="mt-1 text-sm text-error-600">{errors.componentId}</p>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3 border-t border-gray-200 px-6 py-4">
            <button
              type="button"
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
              onClick={onClose}
              disabled={isLoading}
            >
              Hủy
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              className="btn btn-primary flex items-center"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Đang lưu...
                </>
              ) : (
                <>
                  <Save size={16} className="mr-2" />
                  {action ? 'Cập nhật' : 'Thêm mới'}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Delete Confirmation using ConfirmDialog component



// Action Card Component
function ActionCard({
  action,
  readOnly,
  onEdit,
  onDelete,
  components
}: {
  action: JourneyAction;
  readOnly?: boolean;
  onEdit?: (action: JourneyAction) => void;
  onDelete?: (action: JourneyAction) => void;
  components?: Component[];
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: action.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onEdit?.(action);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onDelete?.(action);
  };

  // Find component to get color
  const component = components?.find(c => c.id === action.component_id);
  const componentColor = component?.color_code || '#007bff';
  const componentName = component?.name || action.component_name || 'Unknown';

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className="action-card"
    >
      <div
        className={`relative border border-gray-300 rounded-lg p-3 my-2 select-none text-sm leading-relaxed shadow-sm transition-shadow duration-200 hover:shadow-md group ${
          isDragging ? 'shadow-lg opacity-50' : ''
        }`}
        style={{
          backgroundColor: lightenColor(componentColor, 0.1),
          cursor: readOnly ? 'default' : 'grab',
        }}
      >
        {/* Drag handle area - only content area is draggable */}
        <div
          {...listeners}
          className={`absolute inset-0 ${readOnly ? 'cursor-default' : 'cursor-grab'}`}
        />

        {/* Component Tag - Bottom */}
        <div
          className="absolute left-0 right-0 bottom-0 h-6 text-white text-center rounded-b-lg flex items-center justify-center text-xs font-semibold uppercase tracking-wide whitespace-nowrap overflow-hidden text-ellipsis"
          style={{ backgroundColor: componentColor }}
        >
          {componentName}
        </div>

        {/* Action Content */}
        <div className="pb-8 break-words">
          <div className="font-medium text-gray-900">
            {action.content}
          </div>
        </div>

        {/* Floating Action Buttons - Only show on hover */}
        {!readOnly && (
          <div
            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20 flex gap-1 bg-white bg-opacity-90 rounded p-1 shadow-sm"
            onMouseDown={(e) => e.stopPropagation()}
            onClick={(e) => e.stopPropagation()}
          >
            <button
              type="button"
              className="w-6 h-6 flex items-center justify-center text-primary-600 hover:bg-primary-50 rounded transition-colors duration-200"
              title="Chỉnh sửa"
              onClick={handleEdit}
              onMouseDown={(e) => e.stopPropagation()}
              onPointerDown={(e) => e.stopPropagation()}
            >
              <Edit3 size={12} />
            </button>
            <button
              type="button"
              className="w-6 h-6 flex items-center justify-center text-error-600 hover:bg-error-50 rounded transition-colors duration-200"
              title="Xóa"
              onClick={handleDelete}
              onMouseDown={(e) => e.stopPropagation()}
              onPointerDown={(e) => e.stopPropagation()}
            >
              <X size={12} />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// Step Column Component
function StepColumn({
  step,
  actions,
  readOnly,
  onAddAction,
  onEditAction,
  onDeleteAction,
  onUpdateStepTitle,
  onDeleteColumn,
  components
}: {
  step: JourneyStep;
  actions: JourneyAction[];
  readOnly?: boolean;
  onAddAction?: (stepId: string) => void;
  onEditAction?: (action: JourneyAction) => void;
  onDeleteAction?: (action: JourneyAction) => void;
  onUpdateStepTitle?: (stepId: string, title: string) => void;
  onDeleteColumn?: (stepId: string) => void;
  components?: Component[];
}) {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [titleValue, setTitleValue] = useState(step.title);

  const {
    setNodeRef,
  } = useSortable({
    id: step.id,
    data: {
      type: 'Step',
      step,
    },
  });

  const handleTitleEdit = () => {
    if (!readOnly) {
      setIsEditingTitle(true);
      setTitleValue(step.title);
    }
  };

  const handleTitleSave = () => {
    if (titleValue.trim() && onUpdateStepTitle) {
      onUpdateStepTitle(step.id, titleValue.trim());
    }
    setIsEditingTitle(false);
  };

  const handleTitleCancel = () => {
    setTitleValue(step.title);
    setIsEditingTitle(false);
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSave();
    } else if (e.key === 'Escape') {
      handleTitleCancel();
    }
  };

  return (
    <div
      ref={setNodeRef}
      className="flex-shrink-0 w-80 bg-gray-50 rounded-lg border border-gray-200 p-4 mx-2"
      style={{
        backgroundColor: step.color || '#f4f5f7',
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          {isEditingTitle ? (
            <input
              type="text"
              className="w-full px-2 py-1 text-lg font-semibold bg-white border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              value={titleValue}
              onChange={(e) => setTitleValue(e.target.value)}
              onBlur={handleTitleSave}
              onKeyDown={handleTitleKeyDown}
              autoFocus
            />
          ) : (
            <h4
              className={`text-lg font-semibold text-gray-900 ${
                readOnly ? 'cursor-default' : 'cursor-pointer hover:text-primary-600'
              }`}
              onClick={handleTitleEdit}
              title={readOnly ? '' : 'Click để chỉnh sửa tên cột'}
            >
              {step.title}
            </h4>
          )}
        </div>

        <div className="ml-3">
          <RecordButton readOnly={readOnly} recordCount={actions.length} onClear={() => onDeleteColumn?.(step.id)} />
        </div>
      </div>

      {/* Actions Container */}
      <SortableContext items={actions.map(action => action.id)} strategy={verticalListSortingStrategy}>
        <div className="min-h-fit space-y-2">
          {actions.map((action) => (
            <ActionCard
              key={action.id}
              action={action}
              readOnly={readOnly}
              onEdit={onEditAction}
              onDelete={onDeleteAction}
              components={components}
            />
          ))}
        </div>
      </SortableContext>

      {/* Add Action Button */}
      {!readOnly && (
        <button
          type="button"
          className="w-full mt-4 px-4 py-2 text-sm font-medium text-primary-600 bg-white border border-primary-300 rounded-lg hover:bg-primary-50 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 flex items-center justify-center"
          onClick={() => onAddAction?.(step.id)}
        >
          <Plus size={16} className="mr-2" />
          Thêm hành động
        </button>
      )}
    </div>
  );
}

const JourneyFlowEditor: React.FC<JourneyFlowEditorProps> = ({
  initialData,
  productId,
  onChange,
  readOnly = false
}) => {
  // Initialize with default data first
  const [flowData, setFlowData] = useState<JourneyFlowData>({
    steps: defaultSteps,
    actions: {},
    stepOrder: defaultStepOrder
  });
  const [activeAction, setActiveAction] = useState<JourneyAction | null>(null);
  const isInitialMount = useRef(true);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [components, setComponents] = useState<Component[]>([]);

  // Modal states
  const [showActionModal, setShowActionModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDeleteColumnModal, setShowDeleteColumnModal] = useState(false);
  const [editingAction, setEditingAction] = useState<JourneyAction | null>(null);
  const [deletingAction, setDeletingAction] = useState<JourneyAction | null>(null);
  const [deletingColumn, setDeletingColumn] = useState<{ stepId: string; title: string; actionCount: number } | null>(null);
  const [currentStepId, setCurrentStepId] = useState<string>('');
  const [modalLoading, setModalLoading] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load initial data when component mounts or initialData changes
  useEffect(() => {
    if (initialData && initialData !== '{}' && initialData.trim() !== '') {
      try {
        const parsed = JSON.parse(initialData);
        if (parsed.steps && parsed.actions && parsed.stepOrder) {
          setFlowData(parsed);
          setIsDataLoaded(true);
          return;
        }
      } catch (error) {
        console.error('Error parsing initial data:', error);
      }
    }

    // If no valid initial data, ensure we have default data
    if (!isDataLoaded) {
      setFlowData({
        steps: defaultSteps,
        actions: {},
        stepOrder: defaultStepOrder
      });
      setIsDataLoaded(true);
    }
  }, [initialData, isDataLoaded]);



  // Notify parent of changes (skip initial mount and wait for data to load)
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Only notify parent if data has been loaded
    if (isDataLoaded) {
      const jsonData = JSON.stringify(flowData, null, 2);
      onChange(jsonData);
    }
  }, [flowData, isDataLoaded]); // Remove onChange from dependencies to avoid infinite loop

  // Load components when component mounts
  useEffect(() => {
    loadProductComponents(productId);
  }, []);

  const loadProductComponents = async (productId: number) => {
    try {
      const response = await productService.getProductComponents(productId);
      setComponents(response || []);
    } catch (error) {
      console.error('Error loading components:', error);
      setComponents([]);
    }
  };

  const handleAddAction = (stepId: string) => {
    setCurrentStepId(stepId);
    setEditingAction(null);
    setShowActionModal(true);
  };

  const handleEditAction = (action: JourneyAction) => {
    setEditingAction(action);
    setCurrentStepId(action.stepId);
    setShowActionModal(true);
  };

  const handleSaveActionModal = async (data: { content: string; componentId: number }) => {
    setModalLoading(true);
    try {
      const component = components.find(c => c.id === data.componentId);

      if (editingAction) {
        // Update existing action
        setFlowData(prev => ({
          ...prev,
          actions: {
            ...prev.actions,
            [editingAction.id]: {
              ...editingAction,
              content: data.content,
              component_id: data.componentId,
              component_name: component?.name
            }
          }
        }));
      } else {
        // Create new action
        const actionId = `action-${Date.now()}`;
        const newAction: JourneyAction = {
          id: actionId,
          content: data.content,
          stepId: currentStepId,
          component_id: data.componentId,
          component_name: component?.name
        };

        setFlowData(prev => ({
          ...prev,
          actions: {
            ...prev.actions,
            [actionId]: newAction
          },
          steps: {
            ...prev.steps,
            [currentStepId]: {
              ...prev.steps[currentStepId],
              actionIds: [...prev.steps[currentStepId].actionIds, actionId]
            }
          }
        }));
      }

      setShowActionModal(false);
      setEditingAction(null);
      setCurrentStepId('');
    } catch (error) {
      console.error('Error saving action:', error);
    } finally {
      setModalLoading(false);
    }
  };

  const handleDeleteAction = (action: JourneyAction) => {
    setDeletingAction(action);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!deletingAction) return;

    setModalLoading(true);
    try {
      setFlowData(prev => {
        const { [deletingAction.id]: deletedAction, ...remainingActions } = prev.actions;
        return {
          ...prev,
          actions: remainingActions,
          steps: {
            ...prev.steps,
            [deletingAction.stepId]: {
              ...prev.steps[deletingAction.stepId],
              actionIds: prev.steps[deletingAction.stepId].actionIds.filter(id => id !== deletingAction.id)
            }
          }
        };
      });

      setShowDeleteModal(false);
      setDeletingAction(null);
    } catch (error) {
      console.error('Error deleting action:', error);
    } finally {
      setModalLoading(false);
    }
  };

  const handleCloseActionModal = () => {
    setShowActionModal(false);
    setEditingAction(null);
    setCurrentStepId('');
  };

  const handleCloseDeleteModal = () => {
    setShowDeleteModal(false);
    setDeletingAction(null);
  };

  const handleAddColumn = () => {
    const newStepId = `step${Date.now()}`;
    const newStep: JourneyStep = {
      id: newStepId,
      title: `Bước ${Object.keys(flowData.steps).length + 1}`,
      actionIds: [],
      color: '#f4f5f7'
    };

    setFlowData(prev => ({
      ...prev,
      steps: {
        ...prev.steps,
        [newStepId]: newStep
      },
      stepOrder: [...prev.stepOrder, newStepId]
    }));
  };

  const handleUpdateStepTitle = (stepId: string, title: string) => {
    setFlowData(prev => ({
      ...prev,
      steps: {
        ...prev.steps,
        [stepId]: {
          ...prev.steps[stepId],
          title
        }
      }
    }));
  };

  const handleDeleteColumn = (stepId: string) => {
    // Prevent deleting if it's the last column
    if (flowData.stepOrder.length <= 1) {
      alert('Không thể xóa cột cuối cùng. Phải có ít nhất một cột.');
      return;
    }

    // Set up confirmation dialog
    const step = flowData.steps[stepId];
    const actionCount = step?.actionIds?.length || 0;
    setDeletingColumn({
      stepId,
      title: step.title,
      actionCount
    });
    setShowDeleteColumnModal(true);
  };

  const handleConfirmDeleteColumn = async () => {
    if (!deletingColumn) return;

    setModalLoading(true);
    try {
      setFlowData(prev => {
        // Remove all actions in this step
        const actionsToDelete = prev.steps[deletingColumn.stepId]?.actionIds || [];
        const newActions = { ...prev.actions };
        actionsToDelete.forEach(actionId => {
          delete newActions[actionId];
        });

        // Remove step from steps object
        const newSteps = { ...prev.steps };
        delete newSteps[deletingColumn.stepId];

        // Remove step from stepOrder
        const newStepOrder = prev.stepOrder.filter(id => id !== deletingColumn.stepId);

        return {
          ...prev,
          steps: newSteps,
          actions: newActions,
          stepOrder: newStepOrder
        };
      });

      setShowDeleteColumnModal(false);
      setDeletingColumn(null);
    } catch (error) {
      console.error('Error deleting column:', error);
    } finally {
      setModalLoading(false);
    }
  };

  const handleCloseDeleteColumnModal = () => {
    setShowDeleteColumnModal(false);
    setDeletingColumn(null);
  };

  function handleDragStart(event: DragStartEvent) {
    const { active } = event;
    const action = flowData.actions[active.id as string];
    if (action) {
      setActiveAction(action);
    }
  }

  function handleDragOver(event: DragOverEvent) {
    const { active, over } = event;

    if (!over) return;

    const activeId = active.id;
    const overId = over.id;

    if (activeId === overId) return;

    const activeAction = flowData.actions[activeId as string];
    const overAction = flowData.actions[overId as string];

    if (!activeAction) return;

    // Dragging over another action
    if (overAction && activeAction.stepId !== overAction.stepId) {
      setFlowData(prev => ({
        ...prev,
        actions: {
          ...prev.actions,
          [activeId as string]: {
            ...activeAction,
            stepId: overAction.stepId,
          }
        },
        steps: {
          ...prev.steps,
          [activeAction.stepId]: {
            ...prev.steps[activeAction.stepId],
            actionIds: prev.steps[activeAction.stepId].actionIds.filter(id => id !== activeId),
          },
          [overAction.stepId]: {
            ...prev.steps[overAction.stepId],
            actionIds: [...prev.steps[overAction.stepId].actionIds, activeId as string],
          },
        }
      }));
    }

    // Dragging over a step
    if (flowData.stepOrder.includes(overId as string) && activeAction.stepId !== overId) {
      setFlowData(prev => ({
        ...prev,
        actions: {
          ...prev.actions,
          [activeId as string]: {
            ...activeAction,
            stepId: overId as string,
          }
        },
        steps: {
          ...prev.steps,
          [activeAction.stepId]: {
            ...prev.steps[activeAction.stepId],
            actionIds: prev.steps[activeAction.stepId].actionIds.filter(id => id !== activeId),
          },
          [overId as string]: {
            ...prev.steps[overId as string],
            actionIds: [...prev.steps[overId as string].actionIds, activeId as string],
          },
        }
      }));
    }
  }

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;

    setActiveAction(null);

    if (!over) return;

    const activeId = active.id;
    const overId = over.id;

    if (activeId === overId) return;

    const activeAction = flowData.actions[activeId as string];
    const overAction = flowData.actions[overId as string];

    if (activeAction && overAction && activeAction.stepId === overAction.stepId) {
      const stepId = activeAction.stepId;
      const step = flowData.steps[stepId];

      const oldIndex = step.actionIds.indexOf(activeId as string);
      const newIndex = step.actionIds.indexOf(overId as string);

      setFlowData(prev => ({
        ...prev,
        steps: {
          ...prev.steps,
          [stepId]: {
            ...step,
            actionIds: arrayMove(step.actionIds, oldIndex, newIndex),
          },
        }
      }));
    }
  }

  return (
    <div className="w-full h-full bg-gray-100 p-4">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="flex overflow-x-auto h-[400px]">
          {flowData.stepOrder.map((stepId) => {
            const step = flowData.steps[stepId];
            const stepActions = step.actionIds.map(actionId => flowData.actions[actionId]).filter(Boolean);

            return (
              <StepColumn
                key={step.id}
                step={step}
                actions={stepActions}
                readOnly={readOnly}
                onAddAction={handleAddAction}
                onEditAction={handleEditAction}
                onDeleteAction={handleDeleteAction}
                onUpdateStepTitle={handleUpdateStepTitle}
                onDeleteColumn={handleDeleteColumn}
                components={components}
              />
            );
          })}

          {/* Add Column Button */}
          {!readOnly && (
            <div
              className="flex-shrink-0 w-80 mx-2 border-2 border-dashed border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center cursor-pointer hover:border-primary-400 hover:bg-primary-50 transition-colors duration-200"
              onClick={handleAddColumn}
            >
              <div className="text-gray-400 mb-3">
                <Plus size={32} />
              </div>
              <div className="text-lg font-medium text-gray-700 mb-1">Thêm cột mới</div>
              <div className="text-sm text-gray-500">Click để thêm bước mới</div>
            </div>
          )}
        </div>

        <DragOverlay>
          {activeAction ? (
            <div
              className="relative border border-gray-300 rounded-lg p-3 select-none text-sm leading-relaxed shadow-lg opacity-90"
              style={{
                backgroundColor: lightenColor(components.find(c => c.id === activeAction.component_id)?.color_code || '#007bff', 0.1),
              }}
            >
              <div className="pb-8 break-words">
                <div className="font-medium text-gray-900">
                  {activeAction.content}
                </div>
              </div>

              <div
                className="absolute left-0 right-0 bottom-0 h-6 text-white text-center rounded-b-lg flex items-center justify-center text-xs font-semibold uppercase tracking-wide whitespace-nowrap overflow-hidden text-ellipsis"
                style={{ backgroundColor: components.find(c => c.id === activeAction.component_id)?.color_code || '#007bff' }}
              >
                {components.find(c => c.id === activeAction.component_id)?.name || activeAction.component_name || 'Unknown'}
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* Action Modal */}
      <ActionModal
        isOpen={showActionModal}
        onClose={handleCloseActionModal}
        onSave={handleSaveActionModal}
        action={editingAction}
        components={components}
        isLoading={modalLoading}
      />

      {/* Delete Action Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteModal}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
        title="Xác nhận xóa hành động"
        message={`Bạn có chắc chắn muốn xóa hành động "${deletingAction?.content || ''}"? Hành động này không thể hoàn tác.`}
        confirmText="Xóa"
        cancelText="Hủy"
        variant="error"
        isLoading={modalLoading}
      />

      {/* Delete Column Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteColumnModal}
        onClose={handleCloseDeleteColumnModal}
        onConfirm={handleConfirmDeleteColumn}
        title="Xác nhận xóa cột"
        message={
          deletingColumn?.actionCount && deletingColumn.actionCount > 0
            ? `Bạn có chắc chắn muốn xóa cột "${deletingColumn.title}"? Tất cả ${deletingColumn.actionCount} hành động trong cột sẽ bị xóa. Hành động này không thể hoàn tác.`
            : `Bạn có chắc chắn muốn xóa cột "${deletingColumn?.title || ''}"? Hành động này không thể hoàn tác.`
        }
        confirmText="Xóa cột"
        cancelText="Hủy"
        variant="error"
        isLoading={modalLoading}
      />
    </div>
  );
};

export default JourneyFlowEditor;

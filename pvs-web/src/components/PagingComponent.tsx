import React from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface PagingComponentProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  maxVisiblePages?: number;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const PagingComponent: React.FC<PagingComponentProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  maxVisiblePages = 10,
  showFirstLast = true,
  showPrevNext = true,
  size = 'md',
  className = ''
}) => {
  // Tính toán range của các trang sẽ hiển thị
  const getVisiblePages = (): number[] => {
    const pages: number[] = [];

    if (totalPages <= maxVisiblePages) {
      // Nếu tổng số trang <= maxVisiblePages, hiển thị tất cả
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Tính toán để hiển thị currentPage ở giữa nếu có thể
      const halfVisible = Math.floor(maxVisiblePages / 2);
      let startPage = Math.max(1, currentPage - halfVisible);
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      // Điều chỉnh nếu endPage đã đạt tối đa
      if (endPage === totalPages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  const visiblePages = getVisiblePages();
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages;

  // Size classes for buttons
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 18
  };

  const buttonClass = `inline-flex items-center justify-center border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700 focus:z-10 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 ${sizeClasses[size]}`;
  const activeButtonClass = `inline-flex items-center justify-center border border-primary-500 bg-primary-600 text-white hover:bg-primary-700 focus:z-10 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 ${sizeClasses[size]}`;
  const disabledButtonClass = `inline-flex items-center justify-center border border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed ${sizeClasses[size]}`;

  return (
    <div className={`flex justify-center ${className}`}>
      <nav aria-label="Pagination Navigation" className="isolate inline-flex -space-x-px rounded-md shadow-sm">
        {/* First Page Button */}
        {showFirstLast && !isFirstPage && (
          <button
            className={`${buttonClass} rounded-l-md`}
            onClick={() => onPageChange(1)}
            aria-label="Trang đầu"
            title="Trang đầu"
          >
            <ChevronsLeft size={iconSizes[size]} />
          </button>
        )}

        {/* Previous Page Button */}
        {showPrevNext && (
          <button
            className={isFirstPage ? `${disabledButtonClass} ${!showFirstLast ? 'rounded-l-md' : ''}` : `${buttonClass} ${!showFirstLast ? 'rounded-l-md' : ''}`}
            onClick={() => onPageChange(currentPage - 1)}
            disabled={isFirstPage}
            aria-label="Trang trước"
            title="Trang trước"
          >
            <ChevronLeft size={iconSizes[size]} />
          </button>
        )}

        {/* Ellipsis before visible pages (if needed) */}
        {visiblePages[0] > 1 && (
          <>
            <button
              className={buttonClass}
              onClick={() => onPageChange(1)}
            >
              1
            </button>
            {visiblePages[0] > 2 && (
              <span className={`${disabledButtonClass} cursor-default`}>
                ...
              </span>
            )}
          </>
        )}

        {/* Visible Page Numbers */}
        {visiblePages.map((page) => (
          <button
            key={page}
            className={currentPage === page ? activeButtonClass : buttonClass}
            onClick={() => onPageChange(page)}
            aria-label={`Trang ${page}`}
            aria-current={currentPage === page ? 'page' : undefined}
          >
            {page}
          </button>
        ))}

        {/* Ellipsis after visible pages (if needed) */}
        {visiblePages[visiblePages.length - 1] < totalPages && (
          <>
            {visiblePages[visiblePages.length - 1] < totalPages - 1 && (
              <span className={`${disabledButtonClass} cursor-default`}>
                ...
              </span>
            )}
            <button
              className={buttonClass}
              onClick={() => onPageChange(totalPages)}
            >
              {totalPages}
            </button>
          </>
        )}

        {/* Next Page Button */}
        {showPrevNext && (
          <button
            className={isLastPage ? `${disabledButtonClass} ${!showFirstLast ? 'rounded-r-md' : ''}` : `${buttonClass} ${!showFirstLast ? 'rounded-r-md' : ''}`}
            onClick={() => onPageChange(currentPage + 1)}
            disabled={isLastPage}
            aria-label="Trang sau"
            title="Trang sau"
          >
            <ChevronRight size={iconSizes[size]} />
          </button>
        )}

        {/* Last Page Button */}
        {showFirstLast && !isLastPage && (
          <button
            className={`${buttonClass} rounded-r-md`}
            onClick={() => onPageChange(totalPages)}
            aria-label="Trang cuối"
            title="Trang cuối"
          >
            <ChevronsRight size={iconSizes[size]} />
          </button>
        )}
      </nav>
    </div>
  );
};

export default PagingComponent;
import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { clsx } from 'clsx';
import { Badge } from './ui';
import {
  LayoutDashboard,
  Package,
  List,
  Clock,
  TrendingUp,
  Database,
  BarChart3,
  Settings,
  Shield,
  ArrowLeftFromLine,
  ArrowRightFromLine,
  ChevronDown,
  ChevronUp,
  Users,
  Layers,
  Sliders,
  Route,
  X
} from 'lucide-react';

interface SidebarProps {
  isCollapsed: boolean;
  isMobileOpen?: boolean;
  onToggle: () => void;
  onMobileClose?: () => void;
}

interface MenuItem {
  id: string;
  title: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  path?: string;
  badge?: string;
  badgeColor?: string;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: LayoutDashboard,
    path: '/dashboard'
  },
  {
    id: 'products-services',
    title: '<PERSON>ản phẩm dịch vụ',
    icon: Package,
    children: [
      {
        id: 'products-list',
        title: '<PERSON>h sách sản phẩm',
        icon: List,
        path: '/products'
      },
      {
        id: 'products-journey',
        title: 'Hành trình sản phẩm',
        icon: Route,
        path: '/products/journey'
      },
      {
        id: 'products-timeline',
        title: 'Dòng thời gian',
        icon: Clock,
        path: '/products/timeline'
      },
      {
        id: 'products-stats',
        title: 'Thống kê giao dịch',
        icon: TrendingUp,
        path: '/products/stats'
      }
    ]
  },
  {
    id: 'resources',
    title: 'Nguồn lực',
    icon: Database,
    children: [
      {
        id: 'resources-list',
        title: 'Danh sách',
        icon: List,
        path: '/resources'
      },
      {
        id: 'resources-stats',
        title: 'Thống kê',
        icon: BarChart3,
        path: '/resources/stats'
      }
    ]
  },
  {
    id: 'admin',
    title: 'Quản trị',
    icon: Settings,
    children: [
      {
        id: 'admin-components',
        title: 'Thành phần',
        icon: Layers,
        path: '/admin/components'
      },
      {
        id: 'admin-users',
        title: 'Người dùng',
        icon: Users,
        path: '/admin/users'
      },
      {
        id: 'admin-parameters',
        title: 'Tham số',
        icon: Sliders,
        path: '/admin/parameters'
      }
    ]
  }
];

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, isMobileOpen = false, onToggle, onMobileClose }) => {
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());
  const [hoveredMenu, setHoveredMenu] = useState<string | null>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isCollapsed && hoveredMenu && sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        setHoveredMenu(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isCollapsed, hoveredMenu]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  // Close mobile sidebar when route changes
  useEffect(() => {
    if (isMobileOpen && onMobileClose) {
      onMobileClose();
    }
  }, [location.pathname]); // eslint-disable-line react-hooks/exhaustive-deps

  const isActiveRoute = (path?: string) => {
    if (!path) return false;
    return location.pathname === path;
  };

  const isMenuActive = (item: MenuItem): boolean => {
    if (item.path && isActiveRoute(item.path)) return true;
    if (item.children) {
      return item.children.some(child => isActiveRoute(child.path));
    }
    return false;
  };

  const toggleSubmenu = (menuId: string) => {
    if (isCollapsed) {
      // When collapsed, toggle dropdown state on click
      setHoveredMenu(hoveredMenu === menuId ? null : menuId);
      return;
    }

    setExpandedMenus(prev => {
      const newSet = new Set(prev);
      if (newSet.has(menuId)) {
        newSet.delete(menuId);
      } else {
        newSet.add(menuId);
      }
      return newSet;
    });
  };

  const handleMenuMouseEnter = (menuId: string) => {
    if (isCollapsed) {
      // Clear any existing timeout
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
        hoverTimeoutRef.current = null;
      }
      setHoveredMenu(menuId);
    }
  };

  const handleMenuMouseLeave = () => {
    if (isCollapsed) {
      // Set timeout to close dropdown
      hoverTimeoutRef.current = setTimeout(() => {
        setHoveredMenu(null);
      }, 200);
    }
  };

  const handleDropdownMouseEnter = () => {
    if (isCollapsed && hoverTimeoutRef.current) {
      // Cancel the close timeout when entering dropdown
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
  };

  const handleDropdownMouseLeave = () => {
    if (isCollapsed) {
      // Close dropdown when leaving dropdown area
      setHoveredMenu(null);
    }
  };

  return (
    <div
      ref={sidebarRef}
      className={clsx(
        'fixed inset-y-0 left-0 z-50 flex flex-col bg-white border-r border-gray-200 shadow-lg transition-all duration-300',
        isCollapsed ? 'w-16' : 'w-64',
        isMobileOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
      )}
    >
      {/* Mobile close button */}
      {isMobileOpen && (
        <button
          onClick={onMobileClose}
          className="absolute top-4 right-4 p-1 rounded-lg hover:bg-gray-100 md:hidden"
        >
          <X className="h-5 w-5 text-gray-500" />
        </button>
      )}

      {/* Header */}
      <div className="flex items-center px-4 py-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <Shield className="h-5 w-5 text-white" />
            </div>
          </div>
          {!isCollapsed && (
            <div className="flex flex-col">
              <h1 className="text-lg font-bold text-gray-900">PVS Admin</h1>
              <p className="text-xs text-gray-500">Management System</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {menuItems.map((item) => {
          const IconComponent = item.icon;
          const hasChildren = item.children && item.children.length > 0;
          const isExpanded = expandedMenus.has(item.id);
          const isActive = isMenuActive(item);

          return (
            <div key={item.id} className="relative">
              {/* Main menu item */}
              {hasChildren ? (
                <button
                  className={clsx(
                    'nav-link w-full',
                    isActive ? 'nav-link-active' : 'nav-link-inactive',
                    isCollapsed && 'justify-center'
                  )}
                  onClick={() => toggleSubmenu(item.id)}
                  onMouseEnter={() => isCollapsed && handleMenuMouseEnter(item.id)}
                  onMouseLeave={() => isCollapsed && handleMenuMouseLeave()}
                  title={isCollapsed ? item.title : ''}
                  data-menu-id={item.id}
                >
                  <IconComponent className={clsx(
                    'h-5 w-5 flex-shrink-0',
                    !isCollapsed && 'mr-3'
                  )} />
                  {!isCollapsed && (
                    <>
                      <span className="flex-1 text-left">{item.title}</span>
                      {item.badge && (
                        <Badge variant={item.badgeColor as any || 'primary'} size="sm">
                          {item.badge}
                        </Badge>
                      )}
                      <div className="ml-2">
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </div>
                    </>
                  )}
                </button>
              ) : (
                <Link
                  to={item.path || '#'}
                  className={clsx(
                    'nav-link',
                    isActiveRoute(item.path) ? 'nav-link-active' : 'nav-link-inactive',
                    isCollapsed && 'justify-center'
                  )}
                  title={isCollapsed ? item.title : ''}
                >
                  <IconComponent className={clsx(
                    'h-5 w-5 flex-shrink-0',
                    !isCollapsed && 'mr-3'
                  )} />
                  {!isCollapsed && (
                    <>
                      <span className="flex-1">{item.title}</span>
                      {item.badge && (
                        <Badge variant={item.badgeColor as any || 'primary'} size="sm">
                          {item.badge}
                        </Badge>
                      )}
                    </>
                  )}
                </Link>
              )}

              {/* Submenu items - Normal expanded state */}
              {hasChildren && !isCollapsed && isExpanded && (
                <div className="ml-6 mt-2 space-y-1">
                  {item.children!.map((child) => {
                    const ChildIconComponent = child.icon;
                    return (
                      <Link
                        key={child.id}
                        to={child.path || '#'}
                        className={clsx(
                          'flex items-center px-3 py-2 text-sm rounded-lg transition-colors duration-200',
                          isActiveRoute(child.path)
                            ? 'bg-primary-100 text-primary-700 font-medium'
                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                        )}
                      >
                        <ChildIconComponent className="h-4 w-4 mr-3 flex-shrink-0" />
                        <span className="flex-1">{child.title}</span>
                        {child.badge && (
                          <Badge variant={child.badgeColor as any || 'primary'} size="sm">
                            {child.badge}
                          </Badge>
                        )}
                      </Link>
                    );
                  })}
                </div>
              )}

              {/* Submenu items - Collapsed dropdown state */}
              {hasChildren && isCollapsed && hoveredMenu === item.id && (
                <div
                  className="absolute left-10 top-0 ml-2 min-w-56 max-w-72 bg-white border border-gray-200 rounded-r-lg shadow-lg z-50 py-2"
                  onMouseEnter={handleDropdownMouseEnter}
                  onMouseLeave={handleDropdownMouseLeave}
                >
                  <div className="px-4 py-2 border-b border-gray-200">
                    <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                      {item.title}
                    </p>
                  </div>
                  <div className="py-1">
                    {item.children!.map((child) => {
                      const ChildIconComponent = child.icon;
                      return (
                        <Link
                          key={child.id}
                          to={child.path || '#'}
                          className={clsx(
                            'flex items-center px-4 py-2 text-sm transition-colors duration-200',
                            isActiveRoute(child.path)
                              ? 'bg-primary-100 text-primary-700 font-medium'
                              : 'text-gray-700 hover:bg-gray-100'
                          )}
                        >
                          <ChildIconComponent className="h-4 w-4 mr-3 flex-shrink-0" />
                          <span className="flex-1">{child.title}</span>
                          {child.badge && (
                            <Badge variant={child.badgeColor as any || 'primary'} size="sm">
                              {child.badge}
                            </Badge>
                          )}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="px-4 py-4 border-t border-gray-200">
        <button
          onClick={onToggle}
          className="flex items-center justify-center w-full p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? (
            <ArrowRightFromLine className="h-5 w-5" />
          ) : (
            <>
              <ArrowLeftFromLine className="h-5 w-5 mr-2" />
              <span className="text-sm font-medium">Collapse</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};
export default Sidebar;

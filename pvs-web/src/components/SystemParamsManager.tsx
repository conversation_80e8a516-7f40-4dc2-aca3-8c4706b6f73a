import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, AlertCircle, CheckCircle, Settings, Users, Layers, Activity, UserCheck } from 'lucide-react';
import { systemParamService } from '../services';
import type { SystemParam, SystemParamRequest, ParamType } from '../services';
import SystemParamModal from './SystemParamModal';
import { ConfirmDialog } from './ui';


interface SystemParamsManagerProps {
  className?: string;
}

const SystemParamsManager: React.FC<SystemParamsManagerProps> = ({ className = '' }) => {
  const [activeTab, setActiveTab] = useState<ParamType>('COMPONENT_TYPE');
  const [params, setParams] = useState<SystemParam[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showActiveOnly, setShowActiveOnly] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingParam, setEditingParam] = useState<SystemParam | null>(null);
  const [deletingParam, setDeletingParam] = useState<SystemParam | null>(null);

  // Param types configuration
  const paramTypeConfig = {
    COMPONENT_TYPE: {
      label: 'Loại thành phần',
      icon: <Settings size={18} />,
      color: 'primary',
      description: 'Loại hệ thống/thành phần'
    },
    TARGET_USER: {
      label: 'Nhóm người dùng',
      icon: <Users size={18} />,
      color: 'success',
      description: 'Nhóm người dùng sử dụng hệ thống'
    },
    STAGE_TYPE: {
      label: 'Giai đoạn',
      icon: <Layers size={18} />,
      color: 'info',
      description: 'Giai đoạn của sản phẩm'
    },
    PRODUCT_STATUS: {
      label: 'Trạng thái sản phẩm',
      icon: <Activity size={18} />,
      color: 'warning',
      description: 'Trạng thái của sản phẩm'
    },
    STAKEHOLDER_ROLE: {
      label: 'Vai trò',
      icon: <UserCheck size={18} />,
      color: 'danger',
      description: 'Vai trò của người tham gia sản phẩm'
    }
  };

  // Load parameters for active tab
  useEffect(() => {
    loadParams();
  }, [activeTab, showActiveOnly]);

  const loadParams = async () => {
    setLoading(true);
    try {
      const response = await systemParamService.getSystemParamsByType(activeTab);
      let filteredParams = response.data;

      if (showActiveOnly) {
        filteredParams = filteredParams.filter(param => param.is_active);
      }

      setParams(filteredParams);
    } catch (error) {
      console.error('Error loading parameters:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter parameters based on search term
  const filteredParams = params.filter(param =>
    param.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    param.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToggleStatus = async (param: SystemParam) => {
    try {
      await systemParamService.toggleSystemParamStatus(param.id, !param.is_active);
      await loadParams();
    } catch (error) {
      console.error('Error toggling parameter status:', error);
    }
  };

  const handleDelete = (param: SystemParam) => {
    setDeletingParam(param);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!deletingParam) return;

    try {
      await systemParamService.deleteSystemParam(deletingParam.id);
      await loadParams();
      setShowDeleteModal(false);
      setDeletingParam(null);
    } catch (error) {
      console.error('Error deleting parameter:', error);
    }
  };

  const handleCreate = () => {
    setEditingParam(null);
    setShowModal(true);
  };

  const handleEdit = (param: SystemParam) => {
    setEditingParam(param);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingParam(null);
  };

  const handleSaveParam = async (data: SystemParamRequest) => {
    if (editingParam) {
      // Update existing parameter
      await systemParamService.updateSystemParam(editingParam.id, data);
    } else {
      // Create new parameter
      await systemParamService.createSystemParam(data);
    }
    await loadParams();
  };

  return (
    <div className={className}>
      {/* Tabs */}
      <div className="card mb-6">
        <div className="p-0">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {Object.entries(paramTypeConfig).map(([type, config]) => (
                <button
                  key={type}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center ${
                    activeTab === type
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab(type as ParamType)}
                  type="button"
                >
                  <span className="mr-2">
                    {config.icon}
                  </span>
                  {config.label}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="card">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <span className="mr-3">
                  {paramTypeConfig[activeTab].icon}
                </span>
                {paramTypeConfig[activeTab].label}
              </h2>
              <p className="text-sm text-gray-500 mt-1">{paramTypeConfig[activeTab].description}</p>
            </div>
            <button
              type="button"
              className="btn btn-primary flex items-center"
              onClick={handleCreate}
            >
              <Plus size={16} className="mr-2" />
              Thêm mới
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Search and Filter */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tìm kiếm tham số
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  placeholder="Tìm kiếm theo tên hoặc mã..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex items-end">
              <div className="flex items-center">
                <input
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mr-3"
                  type="checkbox"
                  id="showActiveOnly"
                  checked={showActiveOnly}
                  onChange={(e) => setShowActiveOnly(e.target.checked)}
                />
                <label className="text-sm font-medium text-gray-700" htmlFor="showActiveOnly">
                  Chỉ hiển thị tham số đang hoạt động
                </label>
              </div>
            </div>
          </div>

          {/* Parameters Table */}
          {loading ? (
            <div className="text-center py-12">
              <div className="spinner w-8 h-8 mx-auto" role="status">
                <span className="sr-only">Loading...</span>
              </div>
              <p className="mt-3 text-gray-600">Đang tải dữ liệu...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-header-cell">Mã</th>
                    <th className="table-header-cell">Tên</th>
                    <th className="table-header-cell">Thứ tự</th>
                    <th className="table-header-cell">Trạng thái</th>
                    <th className="table-header-cell">Cập nhật</th>
                    <th className="table-header-cell">Thao tác</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {filteredParams.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="table-cell text-center py-12 text-gray-500">
                        <div className="flex flex-col items-center">
                          <Settings size={48} className="mb-3 opacity-50" />
                          <h5 className="text-lg font-medium text-gray-700 mb-2">
                            {searchTerm ? 'Không tìm thấy tham số nào' : 'Chưa có tham số nào'}
                          </h5>
                          <p className="text-gray-500">
                            {searchTerm ? 'Thử thay đổi từ khóa tìm kiếm' : 'Bắt đầu bằng cách thêm tham số đầu tiên'}
                          </p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    filteredParams.map((param) => (
                      <tr key={param.id} className="hover:bg-gray-50">
                        <td className="table-cell">
                          <code className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs font-mono">{param.code}</code>
                        </td>
                        <td className="table-cell">
                          <span className="font-semibold text-gray-900">{param.name}</span>
                        </td>
                        <td className="table-cell">
                          <span className="badge badge-gray">{param.display_order}</span>
                        </td>
                        <td className="table-cell">
                          <button
                            type="button"
                            className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-colors duration-200 ${
                              param.is_active
                                ? 'bg-success-100 text-success-800 hover:bg-success-200'
                                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                            }`}
                            onClick={() => handleToggleStatus(param)}
                            title={param.is_active ? 'Đang hoạt động - Click để tạm dừng' : 'Không hoạt động - Click để kích hoạt'}
                          >
                            {param.is_active ? (
                              <>
                                <CheckCircle size={12} className="mr-1" />
                                Hoạt động
                              </>
                            ) : (
                              <>
                                <AlertCircle size={12} className="mr-1" />
                                Tạm dừng
                              </>
                            )}
                          </button>
                        </td>
                        <td className="table-cell">
                          <span className="text-sm text-gray-500">
                            {new Date(param.updated_at).toLocaleDateString('vi-VN')}
                          </span>
                        </td>
                        <td className="table-cell">
                          <div className="flex gap-2">
                            <button
                              type="button"
                              className="inline-flex items-center justify-center w-8 h-8 text-primary-600 bg-primary-50 border border-primary-200 rounded-lg hover:bg-primary-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                              onClick={() => handleEdit(param)}
                              title="Chỉnh sửa"
                            >
                              <Edit size={14} />
                            </button>
                            <button
                              type="button"
                              className="inline-flex items-center justify-center w-8 h-8 text-error-600 bg-error-50 border border-error-200 rounded-lg hover:bg-error-100 focus:ring-2 focus:ring-error-500 focus:border-error-500 transition-colors duration-200"
                              onClick={() => handleDelete(param)}
                              title="Xóa"
                            >
                              <Trash2 size={14} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Modal for Create/Edit */}
      <SystemParamModal
        isOpen={showModal}
        onClose={handleCloseModal}
        onSave={handleSaveParam}
        param={editingParam}
        paramType={activeTab}
        title={editingParam ? 'Chỉnh sửa tham số' : 'Thêm tham số mới'}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingParam(null);
        }}
        onConfirm={handleConfirmDelete}
        title="Xác nhận xóa tham số"
        message={`Bạn có chắc chắn muốn xóa tham số "${deletingParam?.name || ''}"? Hành động này không thể hoàn tác.`}
        confirmText="Xóa"
        cancelText="Hủy"
        variant="error"
      />
    </div>
  );
};

export default SystemParamsManager;
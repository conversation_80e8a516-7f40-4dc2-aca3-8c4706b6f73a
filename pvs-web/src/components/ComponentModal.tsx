import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle, Link } from 'lucide-react';
import { systemParamService, componentService } from '../services';
import type { Component, ComponentRequest, SystemParam, DropdownItem } from '../services';

interface ComponentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ComponentRequest) => Promise<void>;
  component?: Component | null;
  title: string;
}

const ComponentModal: React.FC<ComponentModalProps> = ({
  isOpen,
  onClose,
  onSave,
  component,
  title
}) => {
  const [formData, setFormData] = useState<ComponentRequest>({
    name: '',
    description: '',
    component_type_code: '',
    target_user_code: '',
    importance: '',
    main_function: '',
    extra_function: '',
    parent_component_id: undefined,
    confluence_url: '',
    color_code: '#007bff'
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [componentTypes, setComponentTypes] = useState<SystemParam[]>([]);
  const [targetUsers, setTargetUsers] = useState<SystemParam[]>([]);
  const [parentComponents, setParentComponents] = useState<DropdownItem[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(false);

  // Load dropdown options
  useEffect(() => {
    if (isOpen) {
      loadDropdownOptions();
    }
  }, [isOpen]);

  // Reset form when modal opens/closes or component changes
  useEffect(() => {
    if (isOpen) {
      if (component) {
        // Edit mode
        setFormData({
          name: component.name,
          description: component.description,
          component_type_code: component.component_type_code,
          target_user_code: component.target_user_code,
          importance: component.importance,
          main_function: component.main_function || '',
          extra_function: component.extra_function || '',
          parent_component_id: component.parent_component_id,
          confluence_url: component.confluence_url || '',
          color_code: component.color_code || '#007bff'
        });
      } else {
        // Create mode
        setFormData({
          name: '',
          description: '',
          component_type_code: '',
          target_user_code: '',
          importance: '',
          main_function: '',
          extra_function: '',
          parent_component_id: undefined,
          confluence_url: '',
          color_code: '#007bff'
        });
      }
      setErrors({});
    }
  }, [isOpen, component]);

  const loadDropdownOptions = async () => {
    setLoadingOptions(true);
    try {
      const [componentTypesRes, targetUsersRes, parentComponentsRes] = await Promise.all([
        systemParamService.getSystemParamsByType('COMPONENT_TYPE'),
        systemParamService.getSystemParamsByType('TARGET_USER'),
        componentService.getComponentsForDropdown()
      ]);

      setComponentTypes(componentTypesRes.data.filter(item => item.is_active));
      setTargetUsers(targetUsersRes.data.filter(item => item.is_active));
      setParentComponents(parentComponentsRes);
    } catch (error) {
      console.error('Error loading dropdown options:', error);
    } finally {
      setLoadingOptions(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên thành phần là bắt buộc';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Mô tả là bắt buộc';
    }

    if (!formData.component_type_code) {
      newErrors.component_type_code = 'Loại thành phần là bắt buộc';
    }

    if (!formData.target_user_code) {
      newErrors.target_user_code = 'Nhóm người dùng là bắt buộc';
    }

    if (formData.confluence_url && !isValidUrl(formData.confluence_url)) {
      newErrors.confluence_url = 'URL Confluence không hợp lệ';
    }

    if (formData.color_code && !isValidHexColor(formData.color_code)) {
      newErrors.color_code = 'Mã màu không hợp lệ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const isValidHexColor = (color: string): boolean => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving component:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ComponentRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal show block" tabIndex={-1} style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">{title}</h5>
            <button
              type="button"
              className="btn btn-close"
              onClick={onClose}
              disabled={loading}
            ></button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              {loadingOptions && (
                <div className="text-center mb-3">
                  <div className="spinner w-4 h-4 inline-block" role="status">
                    <span className="sr-only">Loading...</span>
                  </div>
                  <span className="ml-2">Đang tải dữ liệu...</span>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="mb-3">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Tên thành phần <span className="text-error-600">*</span>
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                        errors.name ? 'border-error-300' : ''
                      }`}
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="VD: Authentication Service"
                      disabled={loading}
                    />
                    {errors.name && (
                      <div className="text-error-600 text-sm mt-1 flex items-center">
                        <AlertCircle size={16} className="mr-1" />
                        {errors.name}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <div className="mb-3">
                    <label htmlFor="color_code" className="block text-sm font-medium text-gray-700 mb-2">
                      Màu sắc
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="color"
                        className="w-16 h-10 border border-gray-300 rounded cursor-pointer"
                        id="color_code"
                        value={formData.color_code}
                        onChange={(e) => handleInputChange('color_code', e.target.value)}
                        disabled={loading}
                      />
                      <input
                        type="text"
                        className={`flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                          errors.color_code ? 'border-error-300' : ''
                        }`}
                        value={formData.color_code}
                        onChange={(e) => handleInputChange('color_code', e.target.value)}
                        placeholder="#007bff"
                        disabled={loading}
                      />
                    </div>
                    {errors.color_code && (
                      <div className="text-error-600 text-sm mt-1 flex items-center">
                        <AlertCircle size={16} className="mr-1" />
                        {errors.color_code}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="mb-3">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Mô tả <span className="text-error-600">*</span>
                </label>
                <textarea
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.description ? 'border-error-300' : ''
                  }`}
                  id="description"
                  rows={3}
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Mô tả chi tiết về thành phần..."
                  disabled={loading}
                />
                {errors.description && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.description}
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="mb-3">
                    <label htmlFor="component_type_code" className="block text-sm font-medium text-gray-700 mb-2">
                      Loại thành phần <span className="text-error-600">*</span>
                    </label>
                    <select
                      className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                        errors.component_type_code ? 'border-error-300' : ''
                      }`}
                      id="component_type_code"
                      value={formData.component_type_code}
                      onChange={(e) => handleInputChange('component_type_code', e.target.value)}
                      disabled={loading || loadingOptions}
                    >
                      <option value="">Chọn loại thành phần</option>
                      {componentTypes.map((type) => (
                        <option key={type.id} value={type.code}>
                          {type.name}
                        </option>
                      ))}
                    </select>
                    {errors.component_type_code && (
                      <div className="text-error-600 text-sm mt-1 flex items-center">
                        <AlertCircle size={16} className="mr-1" />
                        {errors.component_type_code}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <div className="mb-3">
                    <label htmlFor="target_user_code" className="block text-sm font-medium text-gray-700 mb-2">
                      Nhóm người dùng <span className="text-error-600">*</span>
                    </label>
                    <select
                      className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                        errors.target_user_code ? 'border-error-300' : ''
                      }`}
                      id="target_user_code"
                      value={formData.target_user_code}
                      onChange={(e) => handleInputChange('target_user_code', e.target.value)}
                      disabled={loading || loadingOptions}
                    >
                      <option value="">Chọn nhóm người dùng</option>
                      {targetUsers.map((user) => (
                        <option key={user.id} value={user.code}>
                          {user.name}
                        </option>
                      ))}
                    </select>
                    {errors.target_user_code && (
                      <div className="text-error-600 text-sm mt-1 flex items-center">
                        <AlertCircle size={16} className="mr-1" />
                        {errors.target_user_code}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="mb-3">
                <label htmlFor="importance" className="block text-sm font-medium text-gray-700 mb-2">
                  Tầm quan trọng <span className="text-error-600">*</span>
                </label>
                <textarea
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.importance ? 'border-error-300' : ''
                  }`}
                  id="importance"
                  rows={2}
                  value={formData.importance}
                  onChange={(e) => handleInputChange('importance', e.target.value)}
                  placeholder="Mô tả tầm quan trọng của thành phần..."
                  disabled={loading}
                />
                {errors.importance && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.importance}
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="mb-3">
                    <label htmlFor="main_function" className="block text-sm font-medium text-gray-700 mb-2">
                      Chức năng chính
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      id="main_function"
                      value={formData.main_function}
                      onChange={(e) => handleInputChange('main_function', e.target.value)}
                      placeholder="VD: Xác thực người dùng"
                      disabled={loading}
                    />
                  </div>
                </div>

                <div>
                  <div className="mb-3">
                    <label htmlFor="extra_function" className="block text-sm font-medium text-gray-700 mb-2">
                      Chức năng phụ
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      id="extra_function"
                      value={formData.extra_function}
                      onChange={(e) => handleInputChange('extra_function', e.target.value)}
                      placeholder="VD: Quản lý phiên đăng nhập"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="mb-3">
                    <label htmlFor="parent_component_id" className="block text-sm font-medium text-gray-700 mb-2">
                      Thành phần cha
                    </label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      id="parent_component_id"
                      value={formData.parent_component_id || ''}
                      onChange={(e) => handleInputChange('parent_component_id', e.target.value ? parseInt(e.target.value) : undefined)}
                      disabled={loading || loadingOptions}
                    >
                      <option value="">Không có thành phần cha</option>
                      {parentComponents
                        .filter(comp => !component || comp.id !== component.id)
                        .map((comp) => (
                          <option key={comp.id} value={comp.id}>
                            {comp.name}
                          </option>
                        ))}
                    </select>
                  </div>
                </div>

                <div>
                  <div className="mb-3">
                    <label htmlFor="confluence_url" className="block text-sm font-medium text-gray-700 mb-2">
                      URL Confluence
                    </label>
                    <div className="relative">
                      <span className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Link size={16} />
                      </span>
                      <input
                        type="url"
                        className={`w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                          errors.confluence_url ? 'border-error-300' : ''
                        }`}
                        id="confluence_url"
                        value={formData.confluence_url}
                        onChange={(e) => handleInputChange('confluence_url', e.target.value)}
                        placeholder="https://confluence.example.com/..."
                        disabled={loading}
                      />
                    </div>
                    {errors.confluence_url && (
                      <div className="text-error-600 text-sm mt-1 flex items-center">
                        <AlertCircle size={16} className="mr-1" />
                        {errors.confluence_url}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={onClose}
                disabled={loading}
              >
                <X size={16} className="mr-1" />
                Hủy
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading || loadingOptions}
              >
                {loading ? (
                  <>
                    <span className="spinner w-4 h-4 mr-2" role="status"></span>
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <Save size={16} className="mr-1" />
                    {component ? 'Cập nhật' : 'Tạo mới'}
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ComponentModal;
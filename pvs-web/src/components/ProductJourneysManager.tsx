import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Route,
  Filter,
  Calendar
} from 'lucide-react';
import { productJourneyService } from '../services';
import type {
  ProductJourneyListItem,
  ProductJourneyFilters
} from '../services';
import PagingComponent from './PagingComponent';
import PagingInfo from './PagingInfo';
import { usePaging } from '../hooks';

interface ProductJourneysManagerProps {
  productId: number;
  className?: string;
  readOnly?: boolean;
  onJourneysChange?: () => void;
}

const ProductJourneysManager: React.FC<ProductJourneysManagerProps> = ({
  productId,
  className = '',
  readOnly = false,
  onJourneysChange
}) => {
  const navigate = useNavigate();
  const [journeys, setJourneys] = useState<ProductJourneyListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // Use paging hook
  const paging = usePaging({ initialLimit: 5 });

  // Load journeys
  useEffect(() => {
    loadJourneys();
  }, [productId, paging.currentPage, selectedStatus]);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (paging.currentPage === 1) {
        loadJourneys();
      } else {
        paging.firstPage();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const loadJourneys = async () => {
    setLoading(true);
    try {
      const filters: ProductJourneyFilters = {
        ...paging.getPaginationParams(),
        product_id: productId,
        search: searchTerm || undefined,
        status: selectedStatus || undefined
      };

      const response = await productJourneyService.getProductJourneys(filters);
      setJourneys(response.data);
      paging.setTotal(response.total);
    } catch (error) {
      console.error('Error loading journeys:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    navigate(`/products/journey/create?productId=${productId}`);
  };

  const handleEdit = (journey: ProductJourneyListItem) => {
    navigate(`/products/journey/${journey.id}/edit`);
  };

  const handleDelete = async (journey: ProductJourneyListItem) => {
    if (window.confirm(`Bạn có chắc chắn muốn xóa hành trình "${journey.name}"?`)) {
      try {
        await productJourneyService.deleteProductJourney(journey.id);
        await loadJourneys();
        onJourneysChange?.();
      } catch (error) {
        console.error('Error deleting journey:', error);
      }
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <span className="badge badge-success">Hoạt động</span>;
      case 'draft':
        return <span className="badge badge-warning">Nháp</span>;
      case 'archived':
        return <span className="badge badge-gray">Lưu trữ</span>;
      default:
        return <span className="badge badge-primary">{status}</span>;
    }
  };

  return (
    <div className={className}>
      {/* Header */}
      <div className="card-header flex justify-between items-center">
        <div>
          <h5 className="text-lg font-semibold mb-0">
            <Route size={20} className="mr-2" />
            Hành trình sản phẩm
          </h5>
        </div>
        {!readOnly && (
          <button type="button" className="btn btn-sm border border-primary-300 text-primary-600-600 hover:bg-primary-50" onClick={handleCreate}>
            <Plus size={16} />
          </button>
        )}
      </div>

      <div className="p-6">
        {/* Journeys List */}
        {loading ? (
          <div className="text-center py-4">
            <div className="spinner w-8 h-8" role="status">
              <span className="sr-only">Loading...</span>
            </div>
          </div>
        ) : journeys.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            {searchTerm || selectedStatus
              ? 'Không tìm thấy hành trình nào'
              : 'Chưa có hành trình nào'}
          </div>
        ) : (
          <div className="p-0">
            {(journeys.length > 0) && (
              <div className="overflow-x-auto">
                <table className="table table-sm hover:bg-gray-50">
                  <thead>
                    <tr>
                      <th className='table-header-cell'>Hành trình</th>
                      <th className='table-header-cell'>Trạng thái</th>
                      <th className='table-header-cell'>Thao tác</th>
                    </tr>
                  </thead>
                  <tbody>
                    {journeys
                      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
                      .map(journey => (
                        <tr key={journey.id}>
                          <td className='table-cell'>
                            <h6 className="text-lg font-semibold mb-0 mr-3">{journey.name}</h6>
                            <p className="text-gray-600 text-gray-500 mb-2">
                              {journey.description}
                            </p>
                          </td>
                          <td className='table-cell'>
                            {getStatusBadge(journey.status)}
                          </td>
                          <td className='table-cell'>
                            {!readOnly && (
                              <div className="flex gap-1" role="group">
                                <button
                                  type="button"
                                  className="btn btn-sm border border-blue-300 text-blue-600 hover:bg-blue-50"
                                  title="Xem chi tiết"
                                >
                                  <Eye size={14} />
                                </button>
                                <button
                                  type="button"
                                  className="btn btn-sm border border-primary-300 text-primary-600-600 hover:bg-primary-50"
                                  onClick={() => handleEdit(journey)}
                                  title="Chỉnh sửa"
                                >
                                  <Edit size={14} />
                                </button>
                                <button
                                  type="button"
                                  className="btn btn-sm border border-error-300 text-error-600 hover:bg-error-50"
                                  onClick={() => handleDelete(journey)}
                                  title="Xóa"
                                >
                                  <Trash2 size={14} />
                                </button>
                              </div>
                            )}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Pagination */}
        {paging.totalPages > 1 && (
          <div className="flex justify-between items-center mt-4">
            <PagingInfo {...paging.getPagingInfoProps()} />
            <PagingComponent
              {...paging.getPagingComponentProps()}
              maxVisiblePages={3}
              size="sm"
            />
          </div>
        )}
      </div>


    </div>
  );
};

export default ProductJourneysManager;

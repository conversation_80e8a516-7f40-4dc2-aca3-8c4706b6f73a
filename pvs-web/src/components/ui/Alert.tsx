import React from 'react';
import { clsx } from 'clsx';
import { CheckCircle, AlertTriangle, XCircle, Info, X } from 'lucide-react';

export interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'success' | 'warning' | 'error' | 'info';
  title?: string;
  children: React.ReactNode;
  dismissible?: boolean;
  onDismiss?: () => void;
  icon?: boolean;
}

const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  (
    {
      className,
      variant = 'info',
      title,
      children,
      dismissible = false,
      onDismiss,
      icon = true,
      ...props
    },
    ref
  ) => {
    const baseClasses = 'alert';
    
    const variantClasses = {
      success: 'alert-success',
      warning: 'alert-warning',
      error: 'alert-error',
      info: 'alert-info',
    };

    const icons = {
      success: CheckCircle,
      warning: AlertTriangle,
      error: XCircle,
      info: Info,
    };

    const IconComponent = icons[variant];

    return (
      <div
        ref={ref}
        className={clsx(
          baseClasses,
          variantClasses[variant],
          className
        )}
        {...props}
      >
        <div className="flex">
          {icon && (
            <div className="flex-shrink-0">
              <IconComponent className="h-5 w-5" />
            </div>
          )}
          
          <div className={clsx('flex-1', icon && 'ml-3')}>
            {title && (
              <h3 className="text-sm font-medium mb-1">
                {title}
              </h3>
            )}
            <div className={clsx('text-sm', title && 'mt-1')}>
              {children}
            </div>
          </div>
          
          {dismissible && (
            <div className="flex-shrink-0 ml-4">
              <button
                type="button"
                className="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 hover:bg-black hover:bg-opacity-10"
                onClick={onDismiss}
              >
                <span className="sr-only">Dismiss</span>
                <X className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }
);

Alert.displayName = 'Alert';

export default Alert;

import React, { useState } from 'react';
import '../../styles/components/RecordButton.css'; // File CSS để tùy chỉnh giao diện

interface RecordButtonProps {
  recordCount: number;
  onClear: () => void;
  readOnly?: boolean;
}

const RecordButton: React.FC<RecordButtonProps> = ({ recordCount, onClear, readOnly = false }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    onClear();
  };

  return (readOnly ? (
    <span className="record-button">{recordCount}</span>
  ) : (
    <button
      type="button"
      className="record-button"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
      onMouseDown={(e) => e.stopPropagation()}
      onPointerDown={(e) => e.stopPropagation()}
    >
      {isHovered ? '✕' : recordCount}
    </button>
  ));
};

export default RecordButton;
import React from 'react';
import { AlertTriangle, Trash2, CheckCircle, Info } from 'lucide-react';
import Modal from './Modal';
import Alert from './Alert';
import Button from './Button';

export interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'warning' | 'error' | 'info' | 'success';
  isLoading?: boolean;
  icon?: React.ReactNode;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Xác nhận',
  cancelText = 'Hủy',
  variant = 'warning',
  isLoading = false,
  icon
}) => {
  const handleConfirm = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    onConfirm();
  };

  const handleClose = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    onClose();
  };

  const getDefaultIcon = () => {
    switch (variant) {
      case 'error':
        return <Trash2 size={20} />;
      case 'warning':
        return <AlertTriangle size={20} />;
      case 'success':
        return <CheckCircle size={20} />;
      case 'info':
      default:
        return <Info size={20} />;
    }
  };

  const getConfirmButtonVariant = () => {
    switch (variant) {
      case 'error':
        return 'danger' as const;
      case 'warning':
        return 'warning' as const;
      case 'success':
        return 'success' as const;
      case 'info':
      default:
        return 'primary' as const;
    }
  };

  const getAlertVariant = () => {
    switch (variant) {
      case 'error':
        return 'error' as const;
      case 'warning':
        return 'warning' as const;
      case 'success':
        return 'success' as const;
      case 'info':
      default:
        return 'info' as const;
    }
  };

  const defaultTitle = () => {
    switch (variant) {
      case 'error':
        return 'Xác nhận xóa';
      case 'warning':
        return 'Xác nhận thay đổi';
      case 'success':
        return 'Xác nhận';
      case 'info':
      default:
        return 'Thông báo';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={title || defaultTitle()}
      size="sm"
      closeOnOverlayClick={!isLoading}
      showCloseButton={!isLoading}
    >
      <div className="space-y-4">
        <Alert variant={getAlertVariant()} icon={false}>
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              {icon || getDefaultIcon()}
            </div>
            <div className="flex-1">
              <p className="text-sm">{message}</p>
            </div>
          </div>
        </Alert>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            variant="ghost"
            onClick={handleClose}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            variant={getConfirmButtonVariant()}
            onClick={handleConfirm}
            disabled={isLoading}
            loading={isLoading}
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmDialog;

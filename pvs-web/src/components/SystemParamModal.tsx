import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';
import type { SystemParam, SystemParamRequest, ParamType } from '../services';

interface SystemParamModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: SystemParamRequest) => Promise<void>;
  param?: SystemParam | null;
  paramType: ParamType;
  title: string;
}

const SystemParamModal: React.FC<SystemParamModalProps> = ({
  isOpen,
  onClose,
  onSave,
  param,
  paramType,
  title
}) => {
  const [formData, setFormData] = useState<SystemParamRequest>({
    param_type: paramType,
    code: '',
    name: '',
    display_order: 0,
    is_active: true
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when modal opens/closes or param changes
  useEffect(() => {
    if (isOpen) {
      if (param) {
        // Edit mode
        setFormData({
          param_type: param.param_type,
          code: param.code,
          name: param.name,
          display_order: param.display_order,
          is_active: param.is_active
        });
      } else {
        // Create mode
        setFormData({
          param_type: paramType,
          code: '',
          name: '',
          display_order: 0,
          is_active: true
        });
      }
      setErrors({});
    }
  }, [isOpen, param, paramType]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.code.trim()) {
      newErrors.code = 'Mã tham số là bắt buộc';
    } else if (!/^[A-Z0-9_]+$/.test(formData.code)) {
      newErrors.code = 'Mã tham số chỉ được chứa chữ hoa, số và dấu gạch dưới';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Tên tham số là bắt buộc';
    }

    if (formData.display_order < 0) {
      newErrors.display_order = 'Thứ tự hiển thị phải >= 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving parameter:', error);
      // Handle error - could show toast notification
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof SystemParamRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal show block" tabIndex={-1} style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">{title}</h5>
            <button
              type="button"
              className="btn btn-close"
              onClick={onClose}
              disabled={loading}
            ></button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="mb-3">
                    <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-2">
                      Mã tham số <span className="text-error-600">*</span>
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                        errors.code ? 'border-error-300' : ''
                      }`}
                      id="code"
                      value={formData.code}
                      onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                      placeholder="VD: ACTIVE, PENDING..."
                      disabled={loading}
                    />
                    {errors.code && (
                      <div className="text-error-600 text-sm mt-1 flex items-center">
                        <AlertCircle size={16} className="mr-1" />
                        {errors.code}
                      </div>
                    )}
                    <div className="text-xs text-gray-500 mt-1">
                      Chỉ sử dụng chữ hoa, số và dấu gạch dưới
                    </div>
                  </div>
                </div>

                <div>
                  <div className="mb-3">
                    <label htmlFor="display_order" className="block text-sm font-medium text-gray-700 mb-2">
                      Thứ tự hiển thị
                    </label>
                    <input
                      type="number"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                        errors.display_order ? 'border-error-300' : ''
                      }`}
                      id="display_order"
                      value={formData.display_order}
                      onChange={(e) => handleInputChange('display_order', parseInt(e.target.value) || 0)}
                      min="0"
                      disabled={loading}
                    />
                    {errors.display_order && (
                      <div className="text-error-600 text-sm mt-1 flex items-center">
                        <AlertCircle size={16} className="mr-1" />
                        {errors.display_order}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="mb-3">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Tên tham số <span className="text-error-600">*</span>
                </label>
                <input
                  type="text"
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.name ? 'border-error-300' : ''
                  }`}
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="VD: Đang hoạt động, Tạm dừng..."
                  disabled={loading}
                />
                {errors.name && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.name}
                  </div>
                )}
              </div>

              <div className="mb-3">
                <div className="flex items-center">
                  <input
                    className="mr-2"
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                    disabled={loading}
                  />
                  <label className="text-sm font-medium text-gray-700" htmlFor="is_active">
                    Kích hoạt tham số
                  </label>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Chỉ các tham số được kích hoạt mới hiển thị trong hệ thống
                </div>
              </div>

              <div className="alert alert-info">
                <strong>Loại tham số:</strong> {paramType}
                <br />
                <small className="text-gray-500">
                  Loại tham số được xác định tự động và không thể thay đổi
                </small>
              </div>
            </div>

            <div className="modal-footer">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={onClose}
                disabled={loading}
              >
                <X size={16} className="mr-1" />
                Hủy
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="spinner w-4 h-4 mr-2" role="status"></span>
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <Save size={16} className="mr-1" />
                    {param ? 'Cập nhật' : 'Tạo mới'}
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SystemParamModal;
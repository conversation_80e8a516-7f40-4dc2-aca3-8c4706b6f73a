import React from 'react';
import AsyncSelect from 'react-select/async';
import { productService, type ProductSearchResult } from '../services';
import type { SingleValue, ActionMeta } from 'react-select';

interface ProductOption {
  value: number;
  label: string;
  data: ProductSearchResult;
}

interface ProductAutocompleteProps {
  value?: ProductSearchResult | null;
  onChange: (product: ProductSearchResult | null) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const ProductAutocomplete: React.FC<ProductAutocompleteProps> = ({
  value,
  onChange,
  placeholder = "Tìm kiếm sản phẩm...",
  disabled = false,
  className = ""
}) => {

  // Convert ProductSearchResult to option format
  const productToOption = (product: ProductSearchResult): ProductOption => ({
    value: product.id,
    label: product.name,
    data: product
  });

  // Convert value to option format
  const selectedOption = value ? productToOption(value) : null;

  // Load options function for AsyncSelect
  const loadOptions = async (inputValue: string): Promise<ProductOption[]> => {
    if (inputValue.length < 3) {
      return [];
    }

    try {
      const results = await productService.searchProducts(inputValue, 10);
      return results.map(productToOption);
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  };

  // Handle selection change
  const handleChange = (
    newValue: SingleValue<ProductOption>,
    actionMeta: ActionMeta<ProductOption>
  ) => {
    if (actionMeta.action === 'clear' || !newValue) {
      onChange(null);
    } else {
      onChange(newValue.data);
    }
  };

  // Custom option component to show rich product info
  const formatOptionLabel = (option: ProductOption) => (
    <div className="py-1">
      <div className="font-medium text-gray-900">{option.label}</div>
      {option.data.description && (
        <div className="text-sm text-gray-500 truncate">
          {option.data.description}
        </div>
      )}
    </div>
  );

  return (
    <div className={className}>
      <AsyncSelect<ProductOption>
        value={selectedOption}
        onChange={handleChange}
        loadOptions={loadOptions}
        formatOptionLabel={formatOptionLabel}
        placeholder={placeholder}
        isDisabled={disabled}
        isClearable
        cacheOptions
        defaultOptions={false}
        loadingMessage={() => "Đang tìm kiếm..."}
        noOptionsMessage={({ inputValue }) =>
          inputValue.length < 3
            ? "Nhập ít nhất 3 ký tự để tìm kiếm"
            : "Không tìm thấy sản phẩm nào"
        }
        styles={{
          control: (base, state) => ({
            ...base,
            borderColor: state.isFocused ? '#3b82f6' : '#d1d5db',
            boxShadow: state.isFocused ? '0 0 0 2px rgba(59, 130, 246, 0.1)' : 'none',
            '&:hover': {
              borderColor: state.isFocused ? '#3b82f6' : '#9ca3af'
            }
          }),
          option: (base, state) => ({
            ...base,
            backgroundColor: state.isFocused ? '#f3f4f6' : 'white',
            color: '#111827',
            padding: '8px 12px'
          }),
          menu: (base) => ({
            ...base,
            zIndex: 50
          })
        }}
      />
    </div>
  );
};

export default ProductAutocomplete;

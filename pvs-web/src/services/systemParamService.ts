import { BaseService } from './shared/BaseService';
import type {
  SystemParam,
  SystemParamRequest,
  SystemParamFilters,
  ParamType,
  PaginatedResponse,
  ApiResponse
} from './shared/types';

export class SystemParamService extends BaseService {
  private readonly endpoint = '/system-params';

  /**
   * Get all system parameters with pagination and filters
   */
  async getSystemParams(filters: SystemParamFilters = {}): Promise<PaginatedResponse<SystemParam>> {
    return this.getPaginated<SystemParam>(this.endpoint, filters);
  }

  /**
   * Get system parameter by ID
   */
  async getSystemParamById(id: number): Promise<SystemParam> {
    this.validateId(id);
    return this.getWithSuccess<SystemParam>(`${this.endpoint}/${id}`);
  }

  /**
   * Create new system parameter
   */
  async createSystemParam(data: SystemParamRequest): Promise<SystemParam> {
    this.validateRequired(data, ['param_type', 'code', 'name']);
    return this.postWithSuccess<SystemParam>(this.endpoint, data);
  }

  /**
   * Update system parameter
   */
  async updateSystemParam(id: number, data: SystemParamRequest): Promise<SystemParam> {
    this.validateId(id);
    this.validateRequired(data, ['param_type', 'code', 'name']);
    return this.putWithSuccess<SystemParam>(`${this.endpoint}/${id}`, data);
  }

  /**
   * Delete system parameter
   */
  async deleteSystemParam(id: number): Promise<void> {
    this.validateId(id);
    return this.deleteWithSuccess(`${this.endpoint}/${id}`);
  }

  /**
   * Get all parameter types
   */
  async getParamTypes(): Promise<ParamType[]> {
    return this.getWithSuccess<ParamType[]>(`${this.endpoint}/types`);
  }

  /**
   * Get system parameters by type
   */
  async getSystemParamsByType(paramType: ParamType): Promise<PaginatedResponse<SystemParam>> {
    return this.getSystemParams({ type: paramType });
  }

  /**
   * Toggle system parameter active status
   */
  async toggleSystemParamStatus(id: number, isActive: boolean): Promise<void> {
    this.validateId(id);

    try {
      // Get current parameter to update
      const currentParam = await this.getSystemParamById(id);
      const updatedParam: SystemParamRequest = {
        param_type: currentParam.param_type,
        code: currentParam.code,
        name: currentParam.name,
        display_order: currentParam.display_order,
        is_active: isActive
      };

      await this.updateSystemParam(id, updatedParam);
    } catch (error) {
      this.handleError(error, `toggleSystemParamStatus(${id})`);
    }
  }
}

// Export singleton instance
export const systemParamService = new SystemParamService();

import { BaseService } from './shared/BaseService';
import type { DropdownItem, BaseFilters, PaginatedResponse, ApiResponse } from './shared/types';

export interface Component {
  id: number;
  name: string;
  description: string;
  component_type_code: string;
  target_user_code: string;
  importance: string;
  main_function: string;
  extra_function: string;
  parent_component_id?: number;
  parent_component?: Component;
  child_components?: Component[];
  confluence_url: string;
  color_code: string;
  created_at: string;
  updated_at: string;
}

export interface ComponentRequest {
  name: string;
  description: string;
  component_type_code: string;
  target_user_code: string;
  importance?: string;
  main_function?: string;
  extra_function?: string;
  parent_component_id?: number;
  confluence_url?: string;
  color_code?: string;
}

export interface ComponentFilters extends BaseFilters {
  component_type_code?: string;
  target_user_code?: string;
  parent_id?: number;
}

class ComponentService extends BaseService {
  private readonly endpoint = '/components';

  /**
   * Get components with pagination and filters
   */
  async getComponents(filters: ComponentFilters = {}): Promise<PaginatedResponse<Component>> {
    return this.getPaginated<Component>(this.endpoint, filters);
  }

  /**
   * Get component by ID
   */
  async getComponentById(id: number): Promise<Component> {
    this.validateId(id);
    return this.getWithSuccess<Component>(`${this.endpoint}/${id}`);
  }

  /**
   * Create new component
   */
  async createComponent(data: ComponentRequest): Promise<Component> {
    this.validateRequired(data, ['name', 'component_type_code', 'target_user_code']);
    return this.postWithSuccess<Component>(this.endpoint, data);
  }

  /**
   * Update component
   */
  async updateComponent(id: number, data: ComponentRequest): Promise<Component> {
    this.validateId(id);
    this.validateRequired(data, ['name', 'component_type_code', 'target_user_code']);
    return this.putWithSuccess<Component>(`${this.endpoint}/${id}`, data);
  }

  /**
   * Delete component
   */
  async deleteComponent(id: number): Promise<void> {
    this.validateId(id);
    return this.deleteWithSuccess(`${this.endpoint}/${id}`);
  }

  /**
   * Get components for dropdown
   */
  async getComponentsForDropdown(search?: string): Promise<DropdownItem[]> {
    const filters = search ? { search } : {};
    const queryString = this.buildFilters(filters);
    return this.getWithSuccess<DropdownItem[]>(`${this.endpoint}/dropdown${queryString}`);
  }

  /**
   * Get child components of a parent component
   */
  async getChildComponents(parentId: number): Promise<Component[]> {
    this.validateId(parentId);
    return this.getWithSuccess<Component[]>(`${this.endpoint}/${parentId}/children`);
  }
}

// Create and export singleton instance
export const componentService = new ComponentService();
export default componentService;

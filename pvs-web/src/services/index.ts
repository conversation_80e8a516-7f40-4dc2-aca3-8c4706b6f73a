// Export shared utilities
export { apiClient } from './shared/apiClient';
export { BaseService } from './shared/BaseService';
export * from './shared/types';

// Export all services
export { authService } from './authService';
export { componentService } from './componentService';
export { productService } from './productService';
export { productJourneyService } from './productJourneyService';
export { systemParamService } from './systemParamService';
export { userService } from './userService';
export { roleService } from './roleService';
export { mockAuthService } from './mockAuthService';

// Export service types
export type { LoginRequest, UserInfo, AuthResponse } from './authService';
export type {
  ProductInGroup,
  GroupWithProducts,
  GroupedProductsResponse,
  ProductFilters,
  ProductSearchResult,
  ProductDetail,
  ProductComponent,
  ProductStatusLog,
  AddComponentRequest,
  AddStatusLogRequest
} from './productService';
export type {
  Component,
  ComponentRequest,
  ComponentFilters
} from './componentService';
export type {
  ProductJourney,
  ProductJourneyListItem,
  ProductJourneyRequest,
  ProductJourneyFilters
} from './productJourneyService';
export type {
  SystemParam,
  SystemParamRequest,
  SystemParamFilters,
  ParamType
} from './shared/types';
export type {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  UpdateUserRoleRequest,
  UserFilters
} from './userService';
export type {
  Role,
  CreateRoleRequest,
  UpdateRoleRequest,
  UpdateRolePermissionsRequest,
  RoleFilters
} from './roleService';

// Default exports for backward compatibility
export { default as authServiceDefault } from './authService';
export { default as componentServiceDefault } from './componentService';
export { default as productServiceDefault } from './productService';
export { default as productJourneyServiceDefault } from './productJourneyService';

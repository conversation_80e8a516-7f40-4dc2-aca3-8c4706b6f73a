import { BaseService } from './shared/BaseService';
import type { BaseFilters, PaginatedResponse, DropdownItem } from './shared/types';

export interface Role {
  code: string;
  name: string;
  description: string;
  is_system: boolean;
  permissions: string[];
  created_at: string;
  updated_at: string;
}

export interface CreateRoleRequest {
  code: string;
  name: string;
  description: string;
  permissions: string[];
}

export interface UpdateRoleRequest {
  name: string;
  description: string;
  permissions: string[];
}

export interface UpdateRolePermissionsRequest {
  permissions: string[];
}

export interface RoleFilters extends BaseFilters {
  is_system?: boolean;
}

export class RoleService extends BaseService {
  private readonly endpoint = '/roles';

  /**
   * Get all roles with pagination and filters
   */
  async getRoles(filters: RoleFilters = {}): Promise<PaginatedResponse<Role>> {
    return this.getPaginated<Role>(this.endpoint, filters);
  }

  /**
   * Get role by code
   */
  async getRoleByCode(code: string): Promise<Role> {
    if (!code) {
      throw new Error('Role code is required');
    }
    return this.getWithSuccess<Role>(`${this.endpoint}/${code}`);
  }

  /**
   * Create new role
   */
  async createRole(data: CreateRoleRequest): Promise<Role> {
    this.validateRequired(data, ['code', 'name']);
    return this.postWithSuccess<Role>(this.endpoint, data);
  }

  /**
   * Update role
   */
  async updateRole(code: string, data: UpdateRoleRequest): Promise<Role> {
    if (!code) {
      throw new Error('Role code is required');
    }
    this.validateRequired(data, ['name']);
    return this.putWithSuccess<Role>(`${this.endpoint}/${code}`, data);
  }

  /**
   * Delete role
   */
  async deleteRole(code: string): Promise<void> {
    if (!code) {
      throw new Error('Role code is required');
    }
    return this.deleteWithSuccess(`${this.endpoint}/${code}`);
  }

  /**
   * Update role permissions
   */
  async updateRolePermissions(code: string, data: UpdateRolePermissionsRequest): Promise<void> {
    if (!code) {
      throw new Error('Role code is required');
    }
    this.validateRequired(data, ['permissions']);
    
    try {
      await this.patchWithSuccess(`${this.endpoint}/${code}/permissions`, data);
    } catch (error) {
      this.handleError(error, `updateRolePermissions(${code})`);
    }
  }

  /**
   * Get roles for dropdown (simplified format)
   */
  async getRolesForDropdown(): Promise<DropdownItem[]> {
    try {
      const response = await this.getRoles({ limit: 100 }); // Get all roles
      return response.data.map((role, index) => ({
        id: index,
        name: role.name,
        value: role.code
      }));
    } catch (error) {
      this.handleError(error, 'getRolesForDropdown()');
      return [];
    }
  }

  /**
   * Get all available permissions
   */
  async getAvailablePermissions(): Promise<string[]> {
    try {
      // This would typically come from a separate endpoint
      // For now, return common permissions
      return [
        'users.read',
        'users.create',
        'users.update',
        'users.delete',
        'roles.read',
        'roles.create',
        'roles.update',
        'roles.delete',
        'products.read',
        'products.create',
        'products.update',
        'products.delete',
        'components.read',
        'components.create',
        'components.update',
        'components.delete',
        'system.admin'
      ];
    } catch (error) {
      this.handleError(error, 'getAvailablePermissions()');
      return [];
    }
  }
}

// Export singleton instance
export const roleService = new RoleService();

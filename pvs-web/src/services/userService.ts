import { BaseService } from './shared/BaseService';
import type { BaseFilters, PaginatedResponse } from './shared/types';

export interface User {
  id: number;
  username: string;
  full_name: string;
  role_code: string;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
  role?: Role;
}

export interface Role {
  code: string;
  name: string;
  description: string;
  is_system: boolean;
  permissions: string[];
  created_at: string;
  updated_at: string;
}

export interface CreateUserRequest {
  username: string;
  password: string;
  full_name: string;
  role_code: string;
}

export interface UpdateUserRequest {
  full_name: string;
  is_active: boolean;
}

export interface ChangePasswordRequest {
  old_password: string;
  new_password: string;
}

export interface UpdateUserRoleRequest {
  role_code: string;
}

export interface UserFilters extends BaseFilters {
  is_active?: boolean;
  role_code?: string;
  username?: string;
}

export class UserService extends BaseService {
  private readonly endpoint = '/users';

  /**
   * Get all users with pagination and filters
   */
  async getUsers(filters: UserFilters = {}): Promise<PaginatedResponse<User>> {
    return this.getPaginated<User>(this.endpoint, filters);
  }

  /**
   * Get user by ID
   */
  async getUserById(id: number): Promise<User> {
    this.validateId(id);
    return this.getWithSuccess<User>(`${this.endpoint}/${id}`);
  }

  /**
   * Create new user
   */
  async createUser(data: CreateUserRequest): Promise<User> {
    this.validateRequired(data, ['username', 'password', 'full_name', 'role_code']);
    return this.postWithSuccess<User>(this.endpoint, data);
  }

  /**
   * Update user
   */
  async updateUser(id: number, data: UpdateUserRequest): Promise<User> {
    this.validateId(id);
    this.validateRequired(data, ['full_name']);
    return this.putWithSuccess<User>(`${this.endpoint}/${id}`, data);
  }

  /**
   * Delete user (soft delete)
   */
  async deleteUser(id: number): Promise<void> {
    this.validateId(id);
    return this.deleteWithSuccess(`${this.endpoint}/${id}`);
  }

  /**
   * Change user password
   */
  async changePassword(id: number, data: ChangePasswordRequest): Promise<void> {
    this.validateId(id);
    this.validateRequired(data, ['old_password', 'new_password']);

    try {
      await this.patchWithSuccess(`${this.endpoint}/${id}/password`, data);
    } catch (error) {
      this.handleError(error, `changePassword(${id})`);
    }
  }

  /**
   * Update user role
   */
  async updateUserRole(id: number, data: UpdateUserRoleRequest): Promise<void> {
    this.validateId(id);
    this.validateRequired(data, ['role_code']);

    try {
      await this.patchWithSuccess(`${this.endpoint}/${id}/role`, data);
    } catch (error) {
      this.handleError(error, `updateUserRole(${id})`);
    }
  }

  /**
   * Toggle user active status
   */
  async toggleUserStatus(id: number, isActive: boolean): Promise<void> {
    this.validateId(id);

    try {
      // Get current user to update
      const currentUser = await this.getUserById(id);
      const updatedUser: UpdateUserRequest = {
        full_name: currentUser.full_name,
        is_active: isActive
      };

      await this.updateUser(id, updatedUser);
    } catch (error) {
      this.handleError(error, `toggleUserStatus(${id})`);
    }
  }
}

// Export singleton instance
export const userService = new UserService();

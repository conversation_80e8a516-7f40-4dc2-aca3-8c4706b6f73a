import { BaseService } from './shared/BaseService';
import type { BaseFilters } from './shared/types';

// Types specific to product service
export interface ProductInGroup {
  id: number;
  name: string;
  description: string;
  current_stage: string;
  status: string;
  is_favorite: boolean;
  has_usage_data: boolean;
}

export interface GroupWithProducts {
  id: number;
  name: string;
  description: string;
  is_favorite: boolean;
  products: ProductInGroup[];
}

export interface GroupedProductsResponse {
  groups: GroupWithProducts[];
  count: number;
  total: number;
}

export interface ProductFilters extends BaseFilters {
  group_id?: number;
  year?: number;
  status?: string;
  is_favorite?: boolean;
  group_is_favorite?: boolean;
  search?: string;
}

export interface ProductSearchResult {
  id: number;
  name: string;
  description: string;
  status: string;
  is_favorite: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProductComponent {
  id: number;
  name: string;
  description: string;
  component_type_code: string;
  target_user_code: string;
  importance: string;
  main_function: string;
  extra_function: string;
  parent_component_id?: number;
  confluence_url: string;
  color_code: string;
  created_at: string;
  updated_at: string;
}

export interface ProductStatusLog {
  id: number;
  product_id: number;
  status: string;
  changed_by: string;
  changed_at: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface ProductDetail {
  id: number;
  name: string;
  description: string;
  status: string;
  is_favorite: boolean;
  components: ProductComponent[];
  status_logs: ProductStatusLog[];
  created_at: string;
  updated_at: string;
}

export interface AddComponentRequest {
  component_id: number;
}

export interface AddStatusLogRequest {
  status: string;
  changed_by: string;
  note: string;
}

class ProductService extends BaseService {
  // Get grouped products with filters and pagination
  async getGroupedProducts(filters: ProductFilters = {}): Promise<GroupedProductsResponse> {
    try {
      // Extract group_is_favorite for client-side filtering
      const { group_is_favorite, ...serverFilters } = filters;

      const queryString = this.buildFilters(serverFilters);
      const response = await this.client.getWithSuccessCheck<GroupedProductsResponse>(
        `/products/grouped${queryString}`
      );

      // Apply client-side group_is_favorite filter
      if (group_is_favorite !== undefined) {
        response.groups = response.groups.filter(group => group.is_favorite === group_is_favorite);
      }

      return response;
    } catch (error) {
      this.handleError(error, 'getGroupedProducts');
    }
  }

  // Toggle product favorite status
  async toggleProductFavorite(productId: number, isFavorite: boolean): Promise<void> {
    return this.toggleProperty('/products', productId, 'favorite', isFavorite, 'toggleProductFavorite');
  }

  // Toggle product group favorite status
  async toggleProductGroupFavorite(groupId: number, isFavorite: boolean): Promise<void> {
    return this.toggleProperty('/product-groups', groupId, 'favorite', isFavorite, 'toggleProductGroupFavorite');
  }

  // Get product groups for filter dropdown
  async getProductGroups(): Promise<{ id: number; name: string; description: string }[]> {
    return this.getWithSuccess<{ id: number; name: string; description: string }[]>('/product-groups');
  }

  // Search products by name and description
  async searchProducts(query: string, limit: number = 10): Promise<ProductSearchResult[]> {
    if (query.length < 3) {
      throw new Error('Search query must be at least 3 characters');
    }

    const queryString = this.buildFilters({ q: query, limit });
    return this.getWithSuccess<ProductSearchResult[]>(`/products/search${queryString}`);
  }

  // Get product detail by ID with components and status logs
  async getProductDetail(productId: number): Promise<ProductDetail> {
    return this.getWithSuccess<ProductDetail>(`/products/${productId}`);
  }

  // Get product components
  async getProductComponents(productId: number): Promise<ProductComponent[]> {
    return this.getWithSuccess<ProductComponent[]>(`/products/${productId}/components`);
  }

  // Get product status logs
  async getProductStatusLogs(productId: number): Promise<ProductStatusLog[]> {
    return this.getWithSuccess<ProductStatusLog[]>(`/products/${productId}/status-logs`);
  }

  // Add component to product
  async addComponentToProduct(productId: number, request: AddComponentRequest): Promise<void> {
    try {
      await this.client.postWithSuccessCheck(`/products/${productId}/components`, request);
    } catch (error) {
      this.handleError(error, 'addComponentToProduct');
    }
  }

  // Remove component from product
  async removeComponentFromProduct(productId: number, componentId: number): Promise<void> {
    try {
      await this.client.deleteWithSuccessCheck(`/products/${productId}/components/${componentId}`);
    } catch (error) {
      this.handleError(error, 'removeComponentFromProduct');
    }
  }

  // Add status log to product
  async addStatusLogToProduct(productId: number, request: AddStatusLogRequest): Promise<void> {
    try {
      await this.client.postWithSuccessCheck(`/products/${productId}/status-logs`, request);
    } catch (error) {
      this.handleError(error, 'addStatusLogToProduct');
    }
  }

  // Update product basic info
  async updateProduct(productId: number, data: { name: string; description: string; status: string }): Promise<void> {
    try {
      await this.client.putWithSuccessCheck(`/products/${productId}`, data);
    } catch (error) {
      this.handleError(error, 'updateProduct');
    }
  }
}

// Create and export singleton instance
export const productService = new ProductService();
export default productService;

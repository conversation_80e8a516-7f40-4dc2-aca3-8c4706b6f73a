// Common API response types
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: T[];
  count: number;
  total: number;
}

// Common filter types
export interface BaseFilters {
  page?: number;
  limit?: number;
  search?: string;
}

// Common dropdown item type
export interface DropdownItem {
  id: number;
  name: string;
}

// HTTP methods type
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// API error types
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

export class ApiException extends Error {
  public status?: number;
  public code?: string;

  constructor(message: string, status?: number, code?: string) {
    super(message);
    this.name = 'ApiException';
    this.status = status;
    this.code = code;
  }
}

// System Parameter types
export type ParamType =
  | 'COMPONENT_TYPE'
  | 'TARGET_USER'
  | 'STAGE_TYPE'
  | 'PRODUCT_STATUS'
  | 'STAKEHOLDER_ROLE';

export interface SystemParam {
  id: number;
  param_type: ParamType;
  code: string;
  name: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SystemParamRequest {
  param_type: ParamType;
  code: string;
  name: string;
  display_order: number;
  is_active: boolean;
}

export interface SystemParamFilters extends BaseFilters {
  type?: ParamType;
  is_active?: boolean;
}

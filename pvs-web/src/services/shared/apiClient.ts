import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios';
import { type ApiResponse, type PaginatedResponse, ApiException } from './types';

class ApiClient {
  private instance: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';
    this.instance = this.createInstance();
    this.setupInterceptors();
  }

  private createInstance(): AxiosInstance {
    return axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  private setupInterceptors(): void {
    // Request interceptor to include auth token
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.instance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Generic request method
  private async request<T>(
    method: string,
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.instance.request({
        method,
        url,
        data,
        ...config,
      });
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'An error occurred';
      const status = error.response?.status;
      const code = error.response?.data?.code;
      throw new ApiException(message, status, code);
    }
  }

  // Generic request method without auth token
  private async requestWithoutAuth<T>(
    method: string,
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.instance.request({
        method,
        url,
        data,
        ...config,
        headers: {
          ...config?.headers,
          // Explicitly remove Authorization header
          Authorization: undefined,
        },
      });
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'An error occurred';
      const status = error.response?.status;
      const code = error.response?.data?.code;
      throw new ApiException(message, status, code);
    }
  }

  // GET request
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('GET', url, undefined, config);
  }

  // POST request
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('POST', url, data, config);
  }

  // PUT request
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('PUT', url, data, config);
  }

  // PATCH request
  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('PATCH', url, data, config);
  }

  // DELETE request
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('DELETE', url, undefined, config);
  }

  // Helper method to build query parameters
  buildQueryParams(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  }

  // Helper method for API responses with success check
  async getWithSuccessCheck<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.get<ApiResponse<T>>(url, config);
    if (response.success) {
      return response.data;
    } else {
      throw new ApiException(response.message || 'Request failed');
    }
  }

  // Helper method for paginated responses
  async getPaginated<T>(url: string, config?: AxiosRequestConfig): Promise<PaginatedResponse<T>> {
    const response = await this.get<PaginatedResponse<T>>(url, config);
    if (response.success) {
      return response;
    } else {
      throw new ApiException(response.message || 'Request failed');
    }
  }

  // Helper method for POST with success check
  async postWithSuccessCheck<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.post<ApiResponse<T>>(url, data, config);
    if (response.success) {
      return response.data;
    } else {
      throw new ApiException(response.message || 'Request failed');
    }
  }

  // Helper method for PUT with success check
  async putWithSuccessCheck<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.put<ApiResponse<T>>(url, data, config);
    if (response.success) {
      return response.data;
    } else {
      throw new ApiException(response.message || 'Request failed');
    }
  }

  // Helper method for PATCH with success check
  async patchWithSuccessCheck<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.patch<ApiResponse<T>>(url, data, config);
    if (response.success) {
      return response.data;
    } else {
      throw new ApiException(response.message || 'Request failed');
    }
  }

  // Helper method for DELETE with success check
  async deleteWithSuccessCheck(url: string, config?: AxiosRequestConfig): Promise<void> {
    const response = await this.delete<ApiResponse<null>>(url, config);
    if (!response.success) {
      throw new ApiException(response.message || 'Request failed');
    }
  }

  // Methods for requests without authentication
  async postWithoutAuth<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.requestWithoutAuth<T>('POST', url, data, config);
  }

  // Helper method for POST without auth with success check
  async postWithoutAuthAndSuccessCheck<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.postWithoutAuth<ApiResponse<T>>(url, data, config);
    if (response.success) {
      return response.data;
    } else {
      throw new ApiException(response.message || 'Request failed');
    }
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();
export default apiClient;

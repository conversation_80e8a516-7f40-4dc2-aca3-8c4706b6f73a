/* Journey Flow Editor Styles */

/* Main Container */
.journey-flow-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.journey-flow-content {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  min-height: 600px;
  flex: 1;
}

/* Step Column */
.step-column {
  background-color: #f4f5f7;
  border-radius: 8px;
  padding: 8px;
  width: 250px;
  min-width: 250px;
  min-height: 500px;
  max-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  border: 2px solid transparent;
  overflow: hidden;
}

.step-column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.step-column-title-container {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.step-column-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #172b4d;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  flex: 1;
}

.step-column-title:hover {
  background-color: #f0f8ff;
}

.step-column-title.readonly {
  cursor: default;
}

.step-column-title.readonly:hover {
  background-color: transparent;
}

.step-column-title-input {
  font-size: 16px;
  font-weight: 600;
  border: 1px solid #007bff;
  border-radius: 4px;
}

.step-column-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Actions Container */
.step-actions-container {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
  padding-right: 4px;
}

/* Add Action Button */
.add-action-btn {
  border-style: dashed;
  border-width: 2px;
  border-color: #007bff;
  background-color: transparent;
  color: #007bff;
  padding: 8px 12px;
  border-radius: 6px;
  margin-top: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.add-action-btn:hover {
  background-color: #f0f8ff;
  border-color: #0056b3;
  color: #0056b3;
}

/* Add Column Button */
.add-column-container {
  width: 250px;
  min-width: 200px;
  max-width: 300px;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ccc;
  border-radius: 8px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.add-column-container:hover {
  border-color: #007bff;
  background-color: #f0f8ff;
}

.add-column-content {
  text-align: center;
  color: #6c757d;
}

.add-column-icon {
  margin-bottom: 8px;
}

.add-column-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.add-column-subtitle {
  font-size: 12px;
  color: #868e96;
}

/* Action Buttons Container */
.action-buttons-container {
  position: absolute;
  top: 6px;
  right: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 20;
  display: flex;
  gap: 2px;
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Hover effect for action buttons - target the outer action-card div */
.action-card:hover .action-buttons-container {
  opacity: 1 !important;
}

/* Action Buttons */
.action-btn {
  cursor: pointer;
  padding: 3px 6px;
  font-size: 11px;
  border-radius: 3px;
  line-height: 1;
  min-width: auto;
  transition: all 0.15s ease-in-out;
}

.action-btn-edit {
  border: 1px solid #6c757d;
  background-color: #f8f9fa;
  color: #6c757d;
}

.action-btn-edit:hover {
  background-color: #6c757d;
  color: #fff;
  border-color: #6c757d;
}

.action-btn-delete {
  border: 1px solid #dc3545;
  background-color: #f8f9fa;
  color: #dc3545;
}

.action-btn-delete:hover {
  background-color: #dc3545;
  color: #fff;
  border-color: #dc3545;
}

/* Drag Handle */
.drag-handle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 24px;
  cursor: grab;
  z-index: 1;
}

.drag-handle.readonly {
  cursor: default;
}

/* Drag Overlay */
.drag-overlay {
  opacity: 0.8;
  transform: rotate(5deg);
  cursor: grabbing;
}

/* Responsive */
@media (max-width: 768px) {
  .journey-flow-content {
    padding: 12px;
    gap: 12px;
  }

  .step-column {
    width: 200px;
    min-width: 200px;
    padding: 12px;
  }

  .add-column-container {
    width: 200px;
    min-width: 200px;
  }
}
/* Import fonts */
@import '@fontsource/inter/300.css';
@import '@fontsource/inter/400.css';
@import '@fontsource/inter/500.css';
@import '@fontsource/inter/600.css';
@import '@fontsource/inter/700.css';
@import '@fontsource/jetbrains-mono/400.css';
@import '@fontsource/jetbrains-mono/500.css';

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base layer customizations */
@layer base {
  * {
    border-color: theme('colors.gray.200');
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background-color: theme('colors.gray.50');
    color: theme('colors.gray.900');
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: theme('colors.gray.100');
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: theme('colors.gray.300');
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: theme('colors.gray.400');
  }

  /* Focus styles */
  *:focus {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
  }

  /* Selection styles */
  ::selection {
    background-color: theme('colors.primary.100');
    color: theme('colors.primary.900');
  }
}

/* Component layer */
@layer components {
  /* Button components */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }

  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-sm hover:shadow-md;
  }

  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500 shadow-sm hover:shadow-md;
  }

  .btn-error {
    @apply bg-error-600 text-white hover:bg-error-700 focus:ring-error-500 shadow-sm hover:shadow-md;
  }

  .btn-ghost {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-primary-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Card components */
  .card {
    @apply bg-white rounded-xl border border-gray-200 shadow-soft overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Form components */
  .form-input {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 transition-colors duration-200;
  }

  .form-select {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 transition-colors duration-200;
  }

  .form-textarea {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 transition-colors duration-200 resize-none;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .form-error {
    @apply text-sm text-error-600 mt-1;
  }

  .form-help {
    @apply text-sm text-gray-500 mt-1;
  }

  /* Badge components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply bg-error-100 text-error-800;
  }

  .badge-gray {
    @apply bg-gray-100 text-gray-800;
  }

  /* Alert components */
  .alert {
    @apply p-4 rounded-lg border;
  }

  .alert-success {
    @apply bg-success-50 border-success-200 text-success-800;
  }

  .alert-warning {
    @apply bg-warning-50 border-warning-200 text-warning-800;
  }

  .alert-error {
    @apply bg-error-50 border-error-200 text-error-800;
  }

  .alert-info {
    @apply bg-primary-50 border-primary-200 text-primary-800;
  }

  /* Table components */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* Navigation components */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }

  .nav-link-active {
    @apply bg-primary-100 text-primary-700;
  }

  .nav-link-inactive {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100;
  }

  /* Pagination components */
  .pagination-button {
    @apply inline-flex items-center justify-center border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700 focus:z-10 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .pagination-button-active {
    @apply inline-flex items-center justify-center border border-primary-500 bg-primary-600 text-white hover:bg-primary-700 focus:z-10 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .pagination-button-disabled {
    @apply inline-flex items-center justify-center border border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed;
  }

  .pagination-button-sm {
    @apply px-2 py-1 text-xs;
  }

  .pagination-button-md {
    @apply px-3 py-2 text-sm;
  }

  .pagination-button-lg {
    @apply px-4 py-2 text-base;
  }

  /* Journey Flow Editor components */
  .journey-flow-container {
    @apply w-full h-full bg-gray-100 p-4;
  }

  .journey-flow-content {
    @apply flex overflow-x-auto pb-4;
  }

  .step-column {
    @apply flex-shrink-0 w-80 bg-gray-50 rounded-lg border border-gray-200 p-4 mx-2;
  }

  .step-column-header {
    @apply flex items-center justify-between mb-4;
  }

  .step-column-title {
    @apply text-lg font-semibold text-gray-900 cursor-pointer hover:text-primary-600;
  }

  .step-column-title.readonly {
    @apply cursor-default hover:text-gray-900;
  }

  .step-actions-container {
    @apply min-h-32 space-y-2;
  }

  .action-card {
    @apply relative;
  }

  .add-action-btn {
    @apply w-full mt-4 px-4 py-2 text-sm font-medium text-primary-600 bg-white border border-primary-300 rounded-lg hover:bg-primary-50 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 flex items-center justify-center;
  }

  .add-column-container {
    @apply flex-shrink-0 w-80 mx-2 border-2 border-dashed border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center cursor-pointer hover:border-primary-400 hover:bg-primary-50 transition-colors duration-200;
  }

  .add-column-content {
    @apply text-center;
  }

  .add-column-icon {
    @apply text-gray-400 mb-3;
  }

  .add-column-title {
    @apply text-lg font-medium text-gray-700 mb-1;
  }

  .add-column-subtitle {
    @apply text-sm text-gray-500;
  }

  /* Modal components */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50;
  }

  .modal-container {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }

  .modal-content {
    @apply relative bg-white rounded-xl shadow-large max-w-lg mx-auto my-8;
  }

  /* Loading components */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* Utility classes */
  .text-truncate {
    @apply overflow-hidden text-ellipsis whitespace-nowrap;
  }

  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-md border border-white border-opacity-20;
  }

  .gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700;
  }

  .gradient-success {
    @apply bg-gradient-to-r from-success-600 to-success-700;
  }

  .shadow-glow-primary {
    @apply shadow-glow;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }

  /* Layout utilities */
  .layout-main {
    @apply min-h-screen flex flex-col transition-all duration-300;
  }

  .layout-sidebar-space {
    margin-left: 0;
  }

  @media (min-width: 768px) {
    .layout-sidebar-space.sidebar-expanded {
      margin-left: 16rem; /* w-64 = 16rem */
    }

    .layout-sidebar-space.sidebar-collapsed {
      margin-left: 4rem; /* w-16 = 4rem */
    }
  }
}
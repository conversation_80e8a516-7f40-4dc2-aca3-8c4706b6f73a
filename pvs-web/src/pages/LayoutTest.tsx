import React, { useState } from 'react';
import { Card, CardBody } from '../components/ui';
import PagingComponent from '../components/PagingComponent';
import PaginationInfo from '../components/PagingInfo';
import { Search, Filter } from 'lucide-react';

const LayoutTest: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('');
  const totalPages = 10;
  const totalItems = 200;
  const itemsPerPage = 20;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Layout Test Page</h1>
          <p className="text-gray-500 mt-1">Testing sidebar, pagination, and filter components</p>
        </div>
      </div>

      {/* Filter Component Test */}
      <Card>
        <CardBody>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Filter Components Test
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tìm kiếm sản phẩm
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  placeholder="Tìm kiếm thành phần..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Filter Dropdown */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tất cả loại thành phần
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm"
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
              >
                <option value="">Tất cả loại thành phần</option>
                <option value="type1">Loại 1</option>
                <option value="type2">Loại 2</option>
                <option value="type3">Loại 3</option>
              </select>
            </div>

            {/* Another Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tất cả nhóm người dùng
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm"
                value=""
                onChange={() => {}}
              >
                <option value="">Tất cả nhóm người dùng</option>
                <option value="group1">Nhóm 1</option>
                <option value="group2">Nhóm 2</option>
              </select>
            </div>

            {/* Clear Filters Button */}
            <div className="flex items-end">
              <button
                className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 flex items-center justify-center"
                onClick={() => {
                  setSearchTerm('');
                  setSelectedFilter('');
                }}
                title="Xóa bộ lọc"
              >
                <Filter size={16} className="mr-2" />
                Xóa bộ lọc
              </button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Pagination Component Test */}
      <Card>
        <CardBody>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Pagination Components Test
          </h3>

          {/* Pagination Info and Controls */}
          <div className="flex justify-between items-center mb-6">
            <PaginationInfo
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
            />
            <PagingComponent
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              maxVisiblePages={8}
            />
          </div>

          {/* Different sizes */}
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Small Size:</h4>
              <PagingComponent
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                maxVisiblePages={5}
                size="sm"
              />
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Medium Size (Default):</h4>
              <PagingComponent
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                maxVisiblePages={5}
                size="md"
              />
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Large Size:</h4>
              <PagingComponent
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                maxVisiblePages={5}
                size="lg"
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Test Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }, (_, i) => (
          <Card key={i}>
            <CardBody>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Test Card {i + 1}
              </h3>
              <p className="text-gray-600">
                This is a test card to verify that the layout is working correctly.
                The sidebar should not overlap with this content.
              </p>
              <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                <p className="text-sm text-gray-700">
                  Card width: <span className="font-mono">auto</span><br/>
                  Position: <span className="font-mono">relative</span><br/>
                  Z-index: <span className="font-mono">auto</span>
                </p>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Full width test */}
      <Card>
        <CardBody>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Full Width Content Test
          </h3>
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 h-32 rounded-lg flex items-center justify-center">
            <p className="text-white text-xl font-semibold">
              This content should span the full width of the main area
            </p>
          </div>
          <p className="text-gray-600 mt-4">
            This full-width content should not be hidden behind the sidebar.
            The left edge should be clearly visible and not overlapped.
          </p>
        </CardBody>
      </Card>

      {/* Responsive test */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardBody>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Desktop Layout
            </h3>
            <p className="text-gray-600">
              On desktop (md and above), the sidebar should be visible and the main content
              should have appropriate margin-left to avoid overlap.
            </p>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Mobile Layout
            </h3>
            <p className="text-gray-600">
              On mobile, the sidebar should be hidden by default and slide in when
              the menu button is clicked. Main content should use full width.
            </p>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default LayoutTest;

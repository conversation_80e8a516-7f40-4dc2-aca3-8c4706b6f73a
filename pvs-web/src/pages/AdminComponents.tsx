import React from 'react';
import { Layers } from 'lucide-react';
import ComponentsManager from '../components/ComponentsManager';

const AdminComponentsPage: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Layers className="mr-3" size={32} />
            Quản lý thành phần
          </h1>
          <p className="text-gray-500 mt-1">Quản lý các thành phần và module của hệ thống</p>
        </div>
      </div>

      {/* Components Manager */}
      <ComponentsManager />
    </div>
  );
};

export default AdminComponentsPage;

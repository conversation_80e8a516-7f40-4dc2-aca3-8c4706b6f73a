import React from 'react';
import { TrendingUp, DollarSign, Package, ShoppingCart, BarChart3 } from 'lucide-react';

const ProductStatsPage: React.FC = () => {
  return (
    <div className="w-full py-2">
      {/* Page Header */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-semibold mb-0 text-gray-800 flex items-center">
            <TrendingUp className="mr-2" size={24} />
            Thống kê giao dịch
          </h1>
          <p className="text-gray-500 mb-0">Phân tích doanh thu và giao dịch sản phẩm</p>
        </div>
        <div className="flex gap-2">
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 w-auto">
            <option value="7">7 ngày qua</option>
            <option value="30">30 ngày qua</option>
            <option value="90">3 tháng qua</option>
            <option value="365">1 năm qua</option>
          </select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
        <div>
          <div className="card shadow-md h-full py-2 border-l-4 border-primary-600">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1 mr-4">
                  <div className="text-xs font-bold text-primary-600 uppercase mb-1">
                    Tổng doanh thu
                  </div>
                  <div className="text-xl font-bold text-gray-800 mb-0">
                    125,000,000 VNĐ
                  </div>
                </div>
                <div>
                  <DollarSign className="text-primary-600-600" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="card shadow-md h-full py-2 border-l-4 border-success-600">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1 mr-4">
                  <div className="text-xs font-bold text-success-600 uppercase mb-1">
                    Số giao dịch
                  </div>
                  <div className="text-xl font-bold text-gray-800 mb-0">1,234</div>
                </div>
                <div>
                  <ShoppingCart className="text-success-600-600" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="card shadow-md h-full py-2 border-l-4 border-blue-600">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1 mr-4">
                  <div className="text-xs font-bold text-blue-600 uppercase mb-1">
                    Sản phẩm bán chạy
                  </div>
                  <div className="text-xl font-bold text-gray-800 mb-0">89</div>
                </div>
                <div>
                  <Package className="text-blue-600" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="card shadow-md h-full py-2 border-l-4 border-warning-600">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1 mr-4">
                  <div className="text-xs font-bold text-warning-600 uppercase mb-1">
                    Tăng trưởng
                  </div>
                  <div className="text-xl font-bold text-gray-800 mb-0">+15.2%</div>
                </div>
                <div>
                  <TrendingUp className="text-warning-600-600" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Revenue Chart */}
        <div className="lg:col-span-8">
          <div className="card shadow-md mb-4">
            <div className="card-header py-3 flex items-center justify-between">
              <h6 className="m-0 font-semibold text-primary-600-600 flex items-center">
                <BarChart3 className="mr-2" size={20} />
                Biểu đồ doanh thu theo tháng
              </h6>
            </div>
            <div className="p-6">
              <div className="chart-area">
                <div className="flex justify-center items-center" style={{ height: '300px' }}>
                  <p className="text-gray-500">Biểu đồ doanh thu sẽ được hiển thị ở đây</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className="lg:col-span-4">
          <div className="card shadow-md mb-4">
            <div className="card-header py-3">
              <h6 className="m-0 font-semibold text-primary-600-600 flex items-center">
                <Package className="mr-2" size={20} />
                Top sản phẩm bán chạy
              </h6>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <div>
                    <h6 className="mb-1 font-medium text-gray-900">Laptop Gaming XYZ</h6>
                    <small className="text-gray-500">234 giao dịch</small>
                  </div>
                  <span className="badge badge-primary rounded-full">1</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <div>
                    <h6 className="mb-1 font-medium text-gray-900">Smartphone ABC</h6>
                    <small className="text-gray-500">189 giao dịch</small>
                  </div>
                  <span className="badge badge-primary rounded-full">2</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <div>
                    <h6 className="mb-1 font-medium text-gray-900">Tablet DEF</h6>
                    <small className="text-gray-500">156 giao dịch</small>
                  </div>
                  <span className="badge badge-primary rounded-full">3</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200">
                  <div>
                    <h6 className="mb-1 font-medium text-gray-900">Headphone GHI</h6>
                    <small className="text-gray-500">123 giao dịch</small>
                  </div>
                  <span className="badge badge-primary rounded-full">4</span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <div>
                    <h6 className="mb-1 font-medium text-gray-900">Mouse JKL</h6>
                    <small className="text-gray-500">98 giao dịch</small>
                  </div>
                  <span className="badge badge-primary rounded-full">5</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default ProductStatsPage;

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Route, Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react';
import {
  type ProductJourneyListItem,
  type ProductJourneyFilters
} from '../services';
import { PaginationInfo, PagingComponent } from '../components';
import { type PaginatedResponse, productService, productJourneyService } from '../services';
import { ConfirmDialog } from '../components/ui';

const ProductJourneyPage: React.FC = () => {
  const navigate = useNavigate();
  const [data, setData] = useState<PaginatedResponse<ProductJourneyListItem> | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<{ id: number; name: string; description: string }[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingJourney, setDeletingJourney] = useState<ProductJourneyListItem | null>(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Filter states
  const [filters, setFilters] = useState<ProductJourneyFilters>({
    page: 1,
    limit: 20,
    product_id: undefined,
    status: '',
    search: '',
  });

  // Load product journeys
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await productJourneyService.getProductJourneys(filters);
        setData(result);
        setTotalItems(result.total);
        setTotalPages(Math.ceil(result.total / (filters.limit || 20)));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load product journeys');
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [filters]);

  // Load products for filter dropdown
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const result = await productService.getProductGroups();
        setProducts(result);
      } catch (err) {
        console.error('Failed to load products:', err);
      }
    };
    loadProducts();
  }, []);

  const handleFilterChange = (key: keyof ProductJourneyFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filtering
    }));
  };

  const handleDelete = (journey: ProductJourneyListItem) => {
    setDeletingJourney(journey);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!deletingJourney) return;

    try {
      await productJourneyService.deleteProductJourney(deletingJourney.id);
      // Reload data
      const result = await productJourneyService.getProductJourneys(filters);
      setData(result);
      setShowDeleteModal(false);
      setDeletingJourney(null);
    } catch (err) {
      alert('Không thể xóa hành trình: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const handleEditJourney = (journey: ProductJourneyListItem) => {
    navigate(`/products/journey/${journey.id}/edit`);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'badge-success';
      case 'INACTIVE':
        return 'badge-gray';
      case 'DRAFT':
        return 'badge-warning';
      default:
        return 'badge-primary';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center" style={{ height: '400px' }}>
        <div className="spinner w-8 h-8" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-error" role="alert">
        <h4 className="text-lg font-semibold mb-2">Lỗi!</h4>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="w-full py-2">
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-semibold mb-0 text-gray-800 flex items-center">
            <Route className="mr-2" size={28} />
            Hành trình sản phẩm
          </h1>
          <p className="text-gray-500 mb-0">Quản lý hành trình khách hàng cho các sản phẩm</p>
        </div>
      </div>

      {/* Filters */}
      <div className="card mb-4">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tìm kiếm</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Tìm theo tên hoặc mô tả..."
                  value={filters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
              </div>
            </div>

            {/* Product Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sản phẩm</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={filters.product_id || ''}
                onChange={(e) => handleFilterChange('product_id', e.target.value ? parseInt(e.target.value) : undefined)}
              >
                <option value="">Tất cả sản phẩm</option>
                {products.map(product => (
                  <option key={product.id} value={product.id}>
                    {product.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="">Tất cả trạng thái</option>
                <option value="ACTIVE">Hoạt động</option>
                <option value="INACTIVE">Không hoạt động</option>
                <option value="DRAFT">Bản nháp</option>
              </select>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <button
                className="btn btn-secondary w-full"
                onClick={() => setFilters({ page: 1, limit: 20 })}
              >
                <Filter size={16} className="mr-1" />
                Xóa bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      {data && (
        <>
          {/* Table */}
          {data.data.length > 0 ? (
            <div className="card">
              <div className="p-0">
                <div className="overflow-x-auto">
                  <table className="table mb-0">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên hành trình</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mô tả</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Components</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày tạo</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-30">Thao tác</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.data.map(journey => (
                        <tr key={journey.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <strong className="text-gray-900">{journey.name}</strong>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-primary-600-600">{journey.product_name}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-gray-500">
                              {journey.description || 'Không có mô tả'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="badge badge-gray">{journey.component_count} components</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`badge ${getStatusBadgeClass(journey.status)}`}>
                              {journey.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-gray-900">
                            {new Date(journey.created_at).toLocaleDateString('vi-VN')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex gap-1">
                              <button
                                className="btn btn-sm border border-primary-300 text-primary-600-600 hover:bg-primary-50"
                                title="Xem chi tiết"
                              >
                                <Eye size={14} />
                              </button>
                              <button
                                className="btn btn-sm border border-warning-300 text-warning-600-600 hover:bg-warning-50"
                                title="Chỉnh sửa"
                                onClick={() => handleEditJourney(journey)}
                              >
                                <Edit size={14} />
                              </button>
                              <button
                                className="btn btn-sm border border-error-300 text-error-600 hover:bg-error-50"
                                title="Xóa"
                                onClick={() => handleDelete(journey)}
                              >
                                <Trash2 size={14} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          ) : (
            <div className="card">
              <div className="p-6 text-center py-12">
                <Route size={48} className="text-gray-400 mb-3 mx-auto" />
                <h5 className="text-lg font-medium text-gray-700 mb-2">Không có hành trình nào</h5>
                <p className="text-gray-500">Chưa có hành trình nào được tạo cho các sản phẩm.</p>
              </div>
            </div>
          )}

          {/* Pagination Info and Controls */}
          <div className="flex justify-between items-center mt-4">
            <PaginationInfo
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              itemsPerPage={filters.limit || 20}
            />
            <PagingComponent
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              maxVisiblePages={8}
            />
          </div>
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingJourney(null);
        }}
        onConfirm={handleConfirmDelete}
        title="Xác nhận xóa hành trình"
        message={`Bạn có chắc chắn muốn xóa hành trình "${deletingJourney?.name || ''}"? Hành động này không thể hoàn tác.`}
        confirmText="Xóa"
        cancelText="Hủy"
        variant="error"
      />

    </div>
  );
};

export default ProductJourneyPage;

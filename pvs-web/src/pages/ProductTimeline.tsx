import React from 'react';
import { Clock, Calendar, Package, User } from 'lucide-react';

const ProductTimelinePage: React.FC = () => {
  const timelineEvents = [
    {
      id: 1,
      type: 'create',
      title: '<PERSON><PERSON><PERSON> sản phẩm mới',
      description: '<PERSON><PERSON><PERSON> phẩm "Laptop Gaming XYZ" đã được tạo',
      user: 'Nguyễn <PERSON>n <PERSON>',
      time: '2 giờ trước',
      icon: Package,
      color: 'success'
    },
    {
      id: 2,
      type: 'update',
      title: 'Cập nhật giá sản phẩm',
      description: '<PERSON><PERSON><PERSON> sản phẩm "Smartphone ABC" đã được cập nhật từ 10,000,000 VNĐ thành 9,500,000 VNĐ',
      user: 'Trần Thị B',
      time: '4 giờ trước',
      icon: Package,
      color: 'warning'
    },
    {
      id: 3,
      type: 'delete',
      title: '<PERSON><PERSON><PERSON> sản phẩm',
      description: '<PERSON><PERSON>n phẩm "Tablet DEF" đã bị xóa khỏi hệ thống',
      user: '<PERSON><PERSON> Văn C',
      time: '1 ngày trước',
      icon: Package,
      color: 'danger'
    }
  ];

  return (
    <div className="w-full py-2">
      {/* Page Header */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-semibold mb-0 text-gray-800 flex items-center">
            <Clock className="mr-2" size={24} />
            Dòng thời gian sản phẩm
          </h1>
          <p className="text-gray-500 mb-0">Theo dõi các hoạt động liên quan đến sản phẩm</p>
        </div>
        <div className="flex gap-2">
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 w-auto">
            <option value="">Tất cả hoạt động</option>
            <option value="create">Tạo mới</option>
            <option value="update">Cập nhật</option>
            <option value="delete">Xóa</option>
          </select>
          <input
            type="date"
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 w-auto"
          />
        </div>
      </div>

      {/* Timeline */}
      <div className="w-full">
        <div className="w-full">
          <div className="card">
            <div className="card-header">
              <h5 className="text-lg font-semibold mb-0 flex items-center">
                <Calendar className="mr-2" size={20} />
                Lịch sử hoạt động
              </h5>
            </div>
            <div className="p-6">
              <div className="relative pl-0">
                {timelineEvents.map((event, index) => {
                  const IconComponent = event.icon;
                  return (
                    <div key={event.id} className="flex mb-8">
                      <div className="relative mr-4 flex flex-col items-center">
                        <div className={`w-10 h-10 rounded-full bg-${event.color}-600 flex items-center justify-center z-10`}>
                          <IconComponent size={16} className="text-white" />
                        </div>
                        {index < timelineEvents.length - 1 && (
                          <div className="w-0.5 h-15 bg-gray-200 mt-2"></div>
                        )}
                      </div>
                      <div className="flex-1 -mt-1">
                        <div className="card border-0 shadow-sm">
                          <div className="p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h6 className="font-medium mb-0">{event.title}</h6>
                              <small className="text-gray-500">{event.time}</small>
                            </div>
                            <p className="text-gray-500 mb-2">{event.description}</p>
                            <div className="flex items-center">
                              <User size={16} className="mr-2 text-gray-500" />
                              <small className="text-gray-500">{event.user}</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default ProductTimelinePage;

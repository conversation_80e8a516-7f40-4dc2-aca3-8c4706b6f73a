import { useState } from 'react';
import {
  Dnd<PERSON>ontext,
  DragOverlay,
  closestCorners,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragStartEvent,
  type DragEndEvent,
  type DragOverEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface Task {
  id: string;
  content: string;
  columnId: string;
}

interface Column {
  id: string;
  title: string;
  taskIds: string[];
}

// Initial data
const initialTasks: Record<string, Task> = {
  'task-1': { id: 'task-1', content: 'Design homepage', columnId: 'todo' },
  'task-2': { id: 'task-2', content: 'Setup database', columnId: 'todo' },
  'task-3': { id: 'task-3', content: 'Create API endpoints', columnId: 'doing' },
  'task-4': { id: 'task-4', content: 'Write tests', columnId: 'doing' },
  'task-5': { id: 'task-5', content: 'Deploy to production', columnId: 'done' },
};

const initialColumns: Record<string, Column> = {
  'todo': { id: 'todo', title: 'To Do', taskIds: ['task-1', 'task-2'] },
  'doing': { id: 'doing', title: 'Doing', taskIds: ['task-3', 'task-4'] },
  'done': { id: 'done', title: 'Done', taskIds: ['task-5'] },
};

const columnOrder = ['todo', 'doing', 'done'];

// Task Card Component
function TaskCard({ task }: { task: Task }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="task-card"
    >
      <div style={{
        padding: '12px',
        margin: '8px 0',
        backgroundColor: '#fff',
        border: '1px solid #ddd',
        borderRadius: '6px',
        cursor: 'grab',
        userSelect: 'none',
        boxShadow: isDragging ? '0 4px 12px rgba(0,0,0,0.15)' : '0 1px 3px rgba(0,0,0,0.1)',
        fontSize: '14px',
        lineHeight: '1.4',
      }}>
        {task.content}
      </div>
    </div>
  );
}

// Column Component
function BoardColumn({
  column,
  tasks
}: {
  column: Column;
  tasks: Task[];
}) {
  const {
    setNodeRef,
  } = useSortable({
    id: column.id,
    data: {
      type: 'Column',
      column,
    },
  });

  return (
    <div
      ref={setNodeRef}
      style={{
        backgroundColor: '#f4f5f7',
        borderRadius: '8px',
        padding: '16px',
        width: '280px',
        minHeight: '400px',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <h3 style={{
        margin: '0 0 16px 0',
        fontSize: '16px',
        fontWeight: '600',
        color: '#172b4d',
      }}>
        {column.title} ({tasks.length})
      </h3>

      <SortableContext items={tasks.map(task => task.id)} strategy={verticalListSortingStrategy}>
        <div style={{ flex: 1 }}>
          {tasks.map((task) => (
            <TaskCard key={task.id} task={task} />
          ))}
        </div>
      </SortableContext>
    </div>
  );
}

// Main Trello Board Page Component
export default function TrelloBoardPage() {
  const [tasks, setTasks] = useState(initialTasks);
  const [columns, setColumns] = useState(initialColumns);
  const [activeTask, setActiveTask] = useState<Task | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  function handleDragStart(event: DragStartEvent) {
    const { active } = event;
    const task = tasks[active.id as string];
    if (task) {
      setActiveTask(task);
    }
  }

  function handleDragOver(event: DragOverEvent) {
    const { active, over } = event;

    if (!over) return;

    const activeId = active.id;
    const overId = over.id;

    if (activeId === overId) return;

    const activeTask = tasks[activeId as string];
    const overTask = tasks[overId as string];

    if (!activeTask) return;

    // Dragging over another task
    if (overTask && activeTask.columnId !== overTask.columnId) {
      setTasks(prev => ({
        ...prev,
        [activeId as string]: {
          ...activeTask,
          columnId: overTask.columnId,
        }
      }));

      setColumns(prev => {
        const activeColumn = prev[activeTask.columnId];
        const overColumn = prev[overTask.columnId];

        return {
          ...prev,
          [activeTask.columnId]: {
            ...activeColumn,
            taskIds: activeColumn.taskIds.filter(id => id !== activeId),
          },
          [overTask.columnId]: {
            ...overColumn,
            taskIds: [...overColumn.taskIds, activeId as string],
          },
        };
      });
    }

    // Dragging over a column
    if (columnOrder.includes(overId as string) && activeTask.columnId !== overId) {
      setTasks(prev => ({
        ...prev,
        [activeId as string]: {
          ...activeTask,
          columnId: overId as string,
        }
      }));

      setColumns(prev => {
        const activeColumn = prev[activeTask.columnId];
        const overColumn = prev[overId as string];

        return {
          ...prev,
          [activeTask.columnId]: {
            ...activeColumn,
            taskIds: activeColumn.taskIds.filter(id => id !== activeId),
          },
          [overId as string]: {
            ...overColumn,
            taskIds: [...overColumn.taskIds, activeId as string],
          },
        };
      });
    }
  }

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;

    setActiveTask(null);

    if (!over) return;

    const activeId = active.id;
    const overId = over.id;

    if (activeId === overId) return;

    const activeTask = tasks[activeId as string];
    const overTask = tasks[overId as string];

    if (activeTask && overTask && activeTask.columnId === overTask.columnId) {
      const columnId = activeTask.columnId;
      const column = columns[columnId];

      const oldIndex = column.taskIds.indexOf(activeId as string);
      const newIndex = column.taskIds.indexOf(overId as string);

      setColumns(prev => ({
        ...prev,
        [columnId]: {
          ...column,
          taskIds: arrayMove(column.taskIds, oldIndex, newIndex),
        },
      }));
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-semibold mb-0">Trello Board</h1>
        <button className="btn btn-primary">
          <i className="fas fa-plus mr-2"></i>
          Add Card
        </button>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div style={{
          display: 'flex',
          gap: '16px',
          overflowX: 'auto',
          paddingBottom: '16px',
        }}>
          {columnOrder.map((columnId) => {
            const column = columns[columnId];
            const columnTasks = column.taskIds.map(taskId => tasks[taskId]);

            return (
              <BoardColumn
                key={column.id}
                column={column}
                tasks={columnTasks}
              />
            );
          })}
        </div>

        <DragOverlay>
          {activeTask ? (
            <div style={{
              padding: '12px',
              backgroundColor: '#fff',
              border: '1px solid #ddd',
              borderRadius: '6px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              fontSize: '14px',
              lineHeight: '1.4',
              transform: 'rotate(5deg)',
            }}>
              {activeTask.content}
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}

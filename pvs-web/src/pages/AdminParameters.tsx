import React from 'react';
import { Sliders } from 'lucide-react';
import SystemParamsManager from '../components/SystemParamsManager';

const AdminParametersPage: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Sliders className="mr-3" size={32} />
            Quản lý tham số hệ thống
          </h1>
          <p className="text-gray-500 mt-1"><PERSON><PERSON><PERSON> hình các tham số hệ thống theo từng loại</p>
        </div>
      </div>

      {/* System Parameters Manager */}
      <SystemParamsManager />
    </div>
  );
};

export default AdminParametersPage;

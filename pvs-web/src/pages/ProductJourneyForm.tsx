import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Save, AlertCircle, Route } from 'lucide-react';
import { productJourneyService, productService } from '../services';
import JourneyFlowEditor from '../components/JourneyFlowEditor';
import ProductAutocomplete from '../components/ProductAutocomplete';
import type {
  ProductJourneyRequest,
  ProductJourney,
  ProductSearchResult
} from '../services';

const ProductJourneyFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const isEditMode = Boolean(id);
  const productIdFromQuery = searchParams.get('productId');

  const [formData, setFormData] = useState<ProductJourneyRequest>({
    name: '',
    description: '',
    product_id: productIdFromQuery ? parseInt(productIdFromQuery) : 0,
    flow_data: '{}',
    status: 'DRAFT'
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loadingJourney, setLoadingJourney] = useState(false);
  const [journey, setJourney] = useState<ProductJourney | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<ProductSearchResult | null>(null);

  // Load journey details when editing
  useEffect(() => {
    if (isEditMode && id) {
      loadJourneyDetails();
    }
  }, [isEditMode, id]);

  // Initialize selected product if editing
  useEffect(() => {
    if (journey && journey.product_id && journey.product_name) {
      setSelectedProduct({
        id: journey.product_id,
        name: journey.product_name,
        description: '',
        status: 'ACTIVE',
        is_favorite: false,
        created_at: '',
        updated_at: ''
      });
    }
  }, [journey]);

  // Load product info if productId is provided in query string
  useEffect(() => {
    const loadProductInfo = async () => {
      if (productIdFromQuery && !isEditMode) {
        try {
          const productDetail = await productService.getProductDetail(parseInt(productIdFromQuery));
          setSelectedProduct({
            id: productDetail.id,
            name: productDetail.name,
            description: productDetail.description,
            status: productDetail.status,
            is_favorite: productDetail.is_favorite,
            created_at: productDetail.created_at,
            updated_at: productDetail.updated_at
          });
        } catch (error) {
          console.error('Error loading product info:', error);
        }
      }
    };

    loadProductInfo();
  }, [productIdFromQuery, isEditMode]);

  const loadJourneyDetails = async () => {
    if (!id) return;

    setLoadingJourney(true);
    try {
      const journeyDetail = await productJourneyService.getProductJourneyById(parseInt(id));
      setJourney(journeyDetail);
      setFormData({
        name: journeyDetail.name,
        description: journeyDetail.description,
        product_id: journeyDetail.product_id,
        flow_data: journeyDetail.flow_data,
        status: journeyDetail.status
      });
    } catch (error) {
      console.error('Error loading journey details:', error);
      // Redirect back if journey not found
      navigate('/products/journey');
    } finally {
      setLoadingJourney(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên hành trình là bắt buộc';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Mô tả là bắt buộc';
    }

    if (!formData.status) {
      newErrors.status = 'Trạng thái là bắt buộc';
    }

    if (!formData.product_id) {
      newErrors.product_id = 'Sản phẩm là bắt buộc';
    }

    // Validate flow_data is valid JSON
    try {
      JSON.parse(formData.flow_data);
    } catch {
      newErrors.flow_data = 'Dữ liệu flow phải là JSON hợp lệ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      if (isEditMode && id) {
        await productJourneyService.updateProductJourney(parseInt(id), formData);
      } else {
        await productJourneyService.createProductJourney(formData);
      }

      // Navigate back to product detail if productId is provided, otherwise to journey list
      if (productIdFromQuery) {
        navigate(`/products/${productIdFromQuery}`);
      } else {
        navigate('/products/journey');
      }
    } catch (error) {
      console.error('Error saving journey:', error);
      // Handle error - could show toast notification
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ProductJourneyRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleProductSelect = (product: ProductSearchResult | null) => {
    setSelectedProduct(product);
    setFormData(prev => ({
      ...prev,
      product_id: product?.id || 0
    }));

    // Clear error when product is selected
    if (errors.product_id) {
      setErrors(prev => ({
        ...prev,
        product_id: ''
      }));
    }
  };

  const handleCancel = () => {
    // Navigate back to product detail if productId is provided, otherwise to journey list
    if (productIdFromQuery) {
      navigate(`/products/${productIdFromQuery}`);
    } else {
      navigate('/products/journey');
    }
  };

  if (loadingJourney) {
    return (
      <div className="w-full py-2">
        <div className="flex justify-center items-center" style={{ height: '50vh' }}>
          <div className="spinner w-8 h-8" role="status">
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-2">
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <button
            className="btn btn-secondary mr-3"
            onClick={handleCancel}
            disabled={loading}
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h1 className="text-2xl font-semibold mb-0 flex items-center">
              <Route className="mr-2" size={24} />
              {isEditMode ? 'Chỉnh sửa hành trình' : 'Tạo hành trình mới'}
              {selectedProduct && (
                <span className="text-primary-600-600 ml-2">cho sản phẩm {selectedProduct.name}</span>
              )}
            </h1>
            <p className="text-gray-500 mb-0">
              {isEditMode
                ? `Chỉnh sửa: ${journey?.name}${journey?.product_name ? ` cho sản phẩm ${journey.product_name}` : ''}`
                : selectedProduct
                  ? `Tạo hành trình khách hàng cho sản phẩm ${selectedProduct.name}`
                  : 'Tạo hành trình khách hàng mới'
                }
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit}>
        <div className="card">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className={productIdFromQuery ? "md:col-span-1" : "md:col-span-1"}>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Tên hành trình <span className="text-error-600">*</span>
                </label>
                <input
                  type="text"
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.name ? 'border-error-300' : 'border-gray-300'}`}
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="VD: Quy trình đăng ký tài khoản"
                  disabled={loading || loadingJourney}
                />
                {errors.name && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.name}
                  </div>
                )}
              </div>

              <div className={productIdFromQuery ? "md:col-span-1" : "md:col-span-1"}>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Mô tả <span className="text-error-600">*</span>
                </label>
                <input
                  type="text"
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.description ? 'border-error-300' : 'border-gray-300'}`}
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Mô tả ngắn gọn về hành trình"
                  disabled={loading || loadingJourney}
                />
                {errors.description && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.description}
                  </div>
                )}
              </div>

              <div className={productIdFromQuery ? "md:col-span-1" : "md:col-span-1"}>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                  Trạng thái <span className="text-error-600">*</span>
                </label>
                <select
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.status ? 'border-error-300' : 'border-gray-300'}`}
                  id="status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  disabled={loading || loadingJourney}
                >
                  <option value="DRAFT">Nháp</option>
                  <option value="ACTIVE">Hoạt động</option>
                  <option value="INACTIVE">Không hoạt động</option>
                </select>
                {errors.status && (
                  <div className="text-error-600 text-sm mt-1 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.status}
                  </div>
                )}
              </div>
            </div>

            {/* Journey Flow Editor */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thiết kế hành trình <span className="text-error-600">*</span>
              </label>
              <div
                style={{
                  border: errors.flow_data ? '1px solid #dc3545' : '1px solid #dee2e6',
                  borderRadius: '0.375rem',
                  minHeight: '400px',
                }}
              >
                <JourneyFlowEditor
                  key={`${journey?.id || 'new'}-${formData.flow_data.length}`}
                  initialData={formData.flow_data}
                  productId={formData.product_id}
                  onChange={(data) => handleInputChange('flow_data', data)}
                  readOnly={loading || loadingJourney}
                />
              </div>
              {errors.flow_data && (
                <div className="text-error-600 text-sm mt-1 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.flow_data}
                </div>
              )}
            </div>
          </div>

          <div className="card-footer">
            <div className="flex justify-end gap-2">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handleCancel}
                disabled={loading}
              >
                Hủy
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading || loadingJourney}
              >
                {loading ? (
                  <>
                    <span className="spinner w-4 h-4 mr-2" role="status"></span>
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <Save size={16} className="mr-1" />
                    {isEditMode ? 'Cập nhật' : 'Tạo mới'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ProductJourneyFormPage;

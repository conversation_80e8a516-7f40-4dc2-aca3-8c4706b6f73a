import React from 'react';
import { Users } from 'lucide-react';
import UsersManager from '../components/UsersManager';

const AdminUsersPage: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Users className="mr-3" size={32} />
            Quản lý người dùng
          </h1>
          <p className="text-gray-500 mt-1">Quản lý tài khoản và quyền hạn người dùng</p>
        </div>
      </div>

      {/* Users Manager */}
      <UsersManager />
    </div>
  );
};

export default AdminUsersPage;

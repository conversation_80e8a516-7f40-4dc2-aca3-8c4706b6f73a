import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft, Package, Save, X } from 'lucide-react';
import { productService, type ProductDetail } from '../services';
import ProductComponentManager from '../components/ProductComponentManager';
import ProductStatusLogManager from '../components/ProductStatusLogManager';
import ProductJourneysManager from '../components/ProductJourneysManager';

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const isEditMode = location.pathname.includes('/edit');

  const [product, setProduct] = useState<ProductDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  // Form states for editing
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: ''
  });

  // Refresh trigger for child components
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Load product detail
  useEffect(() => {
    if (!id) return;

    const loadProduct = async () => {
      try {
        setLoading(true);
        setError(null);
        const productData = await productService.getProductDetail(parseInt(id));
        setProduct(productData);
        setFormData({
          name: productData.name,
          description: productData.description,
          status: productData.status
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    loadProduct();
  }, [id, refreshTrigger]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    if (!id || !product) return;

    try {
      setSaving(true);
      await productService.updateProduct(parseInt(id), formData);

      // Trigger refresh
      setRefreshTrigger(prev => prev + 1);

      // Switch to view mode
      navigate(`/products/${id}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save product');
    } finally {
      setSaving(false);
    }
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center" style={{ height: '50vh' }}>
        <div className="spinner w-8 h-8" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full py-2">
        <div className="alert alert-error" role="alert">
          <h4 className="text-lg font-semibold mb-2">Lỗi!</h4>
          <p className="mb-4">{error}</p>
          <button type="button" className="btn btn-secondary border border-error-300 text-error-600 hover:bg-error-50" onClick={() => navigate('/products')}>
            Quay lại danh sách
          </button>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="w-full py-2">
        <div className="alert alert alert-warning" role="alert">
          Không tìm thấy sản phẩm
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-2">
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <button
            className="btn btn-secondary mr-3"
            onClick={() => navigate('/products')}
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h1 className="text-2xl font-semibold mb-0 flex items-center">
              <Package className="mr-2" size={24} />
              {isEditMode ? 'Chỉnh sửa sản phẩm' : 'Chi tiết sản phẩm'}
            </h1>
            <p className="text-gray-500 mb-0">{product.name}</p>
          </div>
        </div>

      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <div className="lg:col-span-8">
          <div className="card mb-4">
            <div className="card-header flex justify-between items-center">
              <h5 className="text-lg font-semibold mb-0">Thông tin cơ bản</h5>
              {isEditMode ? (
                <div className="flex gap-2">
                  <button
                    className="btn btn-sm btn-secondary"
                    onClick={() => navigate(`/products/${id}`)}
                    disabled={saving}
                  >
                    <X size={16} className="mr-1" />
                    Hủy
                  </button>
                  <button
                    className="btn btn-sm btn-primary"
                    onClick={handleSave}
                    disabled={saving}
                  >
                    <Save size={16} className="mr-1" />
                    {saving ? 'Đang lưu...' : 'Lưu'}
                  </button>
                </div>
              ) : (
                <button
                  className="btn btn-sm border border-primary-300 text-primary-600-600 hover:bg-primary-50"
                  onClick={() => navigate(`/products/${id}/edit`)}
                >
                  Chỉnh sửa
                </button>
              )}
            </div>
            <div className="p-6">
              {isEditMode ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="mb-3">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">Tên sản phẩm</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="mb-3">
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                    >
                      <option value="ACTIVE">ACTIVE</option>
                      <option value="INACTIVE">INACTIVE</option>
                      <option value="DEVELOPMENT">DEVELOPMENT</option>
                      <option value="MAINTENANCE">MAINTENANCE</option>
                    </select>
                  </div>
                  <div className="col-span-full mb-3">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">Mô tả</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      id="description"
                      name="description"
                      rows={3}
                      value={formData.description}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="mb-3">
                    <strong className="text-gray-700">Tên sản phẩm:</strong>
                    <p className="text-gray-900">{product.name}</p>
                  </div>
                  <div className="mb-3">
                    <strong className="text-gray-700">Trạng thái:</strong>
                    <p>
                      <span className={`badge ${product.status === 'ACTIVE' ? 'badge-success' : 'badge-gray'}`}>
                        {product.status}
                      </span>
                    </p>
                  </div>
                  <div className="col-span-full mb-3">
                    <strong className="text-gray-700">Mô tả:</strong>
                    <p className="text-gray-900">{product.description || 'Không có mô tả'}</p>
                  </div>
                  <div className="mb-3">
                    <strong className="text-gray-700">Ngày tạo:</strong>
                    <p className="text-gray-900">{new Date(product.created_at).toLocaleDateString('vi-VN')}</p>
                  </div>
                  <div className="mb-3">
                    <strong className="text-gray-700">Cập nhật lần cuối:</strong>
                    <p className="text-gray-900">{new Date(product.updated_at).toLocaleDateString('vi-VN')}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
          {/* Status Logs Section */}
          <div className="card mb-4">
            <ProductStatusLogManager
              productId={parseInt(id!)}
              statusLogs={product.status_logs || []}
              onStatusLogsChange={handleRefresh}
              readOnly={isEditMode}
            />
          </div>

          {/* Product Journeys Section */}
          <div className="card">
            <ProductJourneysManager
              productId={parseInt(id!)}
              onJourneysChange={handleRefresh}
              readOnly={isEditMode}
            />
          </div>
        </div>

        {/* Components Section */}
        <div className="lg:col-span-4">
          <ProductComponentManager
            productId={parseInt(id!)}
            components={product.components || []}
            onComponentsChange={handleRefresh}
            readOnly={isEditMode}
          />
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;

import React, { useState, useEffect, useRef } from 'react';
import { Package, ChevronRight, ChevronDown, Star, Eye, Search, Filter } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { productService, type GroupedProductsResponse, type ProductFilters } from '../services/productService';
import PaginationInfo from '../components/PagingInfo';
import PagingComponent from '../components/PagingComponent';

const ProductsPage: React.FC = () => {
  const navigate = useNavigate();
  const [data, setData] = useState<GroupedProductsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [productGroups, setProductGroups] = useState<{ id: number; name: string; description: string }[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<Set<number>>(new Set());

  // Pagination
  const [totalProducts, setTotalProducts] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Filter states
  const [filters, setFilters] = useState<ProductFilters>({
    page: 1,
    limit: 20,
    group_id: undefined,
    year: undefined,
    status: '',
    group_is_favorite: undefined,
    search: '',
  });

  // Ref để lưu vị trí scroll khi pagination
  const scrollPositionRef = useRef<number>(0);

  // Load product groups for filter dropdown
  useEffect(() => {
    const loadProductGroups = async () => {
      try {
        const groups = await productService.getProductGroups();
        setProductGroups(groups);
      } catch (err) {
        console.error('Failed to load product groups:', err);
      }
    };
    loadProductGroups();
  }, []);

  // Load grouped products
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await productService.getGroupedProducts(filters);
        setData(result);
        // Expand all groups that have products by default
        const groupsWithProducts = new Set(
          result.groups
            .filter(group => group.products.length > 0)
            .map(group => group.id)
        );
        setExpandedGroups(groupsWithProducts);
        setTotalProducts(result.total);
        setTotalPages(Math.ceil(result.total / (filters.limit || 20)));

        // Khôi phục vị trí scroll sau khi load xong (chỉ khi pagination)
        if (scrollPositionRef.current > 0) {
          setTimeout(() => {
            window.scrollTo(0, scrollPositionRef.current);
            scrollPositionRef.current = 0;
          }, 100);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load products');
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof ProductFilters, value: any) => {
    // Lưu vị trí scroll hiện tại khi pagination
    if (key === 'page') {
      scrollPositionRef.current = window.scrollY;
    }

    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value, // Reset to page 1 when changing filters
    }));
  };

  // Handle group expand/collapse
  const toggleGroupExpansion = (groupId: number) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  // Handle group favorite toggle
  const handleToggleGroupFavorite = async (groupId: number, currentFavorite: boolean) => {
    try {
      await productService.toggleProductGroupFavorite(groupId, !currentFavorite);
      // Reload data to reflect changes
      const result = await productService.getGroupedProducts(filters);
      setData(result);
      // Update expanded groups to maintain state
      const groupsWithProducts = new Set(
        result.groups
          .filter(group => group.products.length > 0)
          .map(group => group.id)
      );
      setExpandedGroups(groupsWithProducts);
    } catch (err) {
      console.error('Failed to toggle group favorite:', err);
      alert('Failed to update group favorite status');
    }
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'badge-success';
      case 'inactive': return 'badge-gray';
      case 'idle': return 'badge-warning';
      default: return 'badge-gray';
    }
  };

  // Generate year options (from 2006 to current year)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: currentYear - 2006 + 1 }, (_, i) => currentYear - i);

  return (
    <div className="w-full py-2" style={{ scrollBehavior: 'smooth' }}>
      {/* Page Header */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-semibold mb-0 text-gray-800 flex items-center">
            <Package className="mr-2" size={24} />
            Danh sách sản phẩm
          </h1>
          <p className="text-gray-500 mb-0">Quản lý tất cả sản phẩm và dịch vụ</p>
        </div>
      </div>

      {/* Filters Bar */}
      <div className="card mb-4">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search Input */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tìm kiếm sản phẩm
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  placeholder="Tìm kiếm thành phần..."
                  value={filters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
              </div>
            </div>

            {/* Product Group Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tất cả loại thành phần
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm"
                value={filters.group_id || ''}
                onChange={(e) => handleFilterChange('group_id', e.target.value ? parseInt(e.target.value) : undefined)}
              >
                <option value="">Tất cả loại thành phần</option>
                {productGroups.map(group => (
                  <option key={group.id} value={group.id}>{group.name}</option>
                ))}
              </select>
            </div>
            {/* Target User Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tất cả nhóm người dùng
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm"
                value={filters.year || ''}
                onChange={(e) => handleFilterChange('year', e.target.value ? parseInt(e.target.value) : undefined)}
              >
                <option value="">Tất cả nhóm người dùng</option>
                {yearOptions.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            {/* Clear Filters Button */}
            <div className="flex items-end">
              <button
                className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 flex items-center justify-center"
                onClick={() => setFilters({
                  page: 1,
                  limit: 20,
                  group_id: undefined,
                  year: undefined,
                  status: '',
                  group_is_favorite: undefined,
                  search: '',
                })}
                title="Xóa bộ lọc"
              >
                <Filter size={16} className="mr-2" />
                Xóa bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="card">
          <div className="p-6 text-center py-12">
            <div className="spinner w-8 h-8 mx-auto" role="status">
              <span className="sr-only">Loading...</span>
            </div>
            <p className="mt-3 mb-0 text-gray-600">Đang tải dữ liệu...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="card">
          <div className="p-6">
            <div className="bg-error-50 border border-error-200 text-error-700 px-4 py-3 rounded-lg" role="alert">
              <strong>Lỗi:</strong> {error}
            </div>
          </div>
        </div>
      )}

      {/* Products Content */}
      {!loading && !error && data && (
        <>


          {/* No data message */}
          {data.groups.filter(group => group.products.length > 0).length === 0 && (
            <div className="card">
              <div className="p-6 text-center py-12">
                <div className="text-gray-500">
                  <Package size={48} className="mb-3 opacity-50 mx-auto" />
                  <h5 className="text-lg font-medium text-gray-700 mb-2">Không tìm thấy sản phẩm</h5>
                  <p className="mb-0">Không có sản phẩm nào phù hợp với bộ lọc hiện tại.</p>
                </div>
              </div>
            </div>
          )}

          {/* Tree View - Groups and Products */}
          {data.groups.filter(group => group.products.length > 0).length > 0 && (
            <div className="card">
              <div className="p-0">
                <div className="overflow-x-auto">
                  <table className="table mb-0">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="w-10"></th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mô tả</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-30">Yêu thích</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-30">Thao tác</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.groups
                        .filter(group => group.products.length > 0)
                        .map(group => (
                          <React.Fragment key={group.id}>
                            {/* Group Row */}
                            <tr className="bg-gray-100">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <button
                                  className="p-0 bg-transparent border-none text-gray-600 hover:text-gray-800 transition-colors"
                                  onClick={() => toggleGroupExpansion(group.id)}
                                  title={expandedGroups.has(group.id) ? 'Thu gọn' : 'Mở rộng'}
                                >
                                  {expandedGroups.has(group.id) ? (
                                    <ChevronDown size={16} />
                                  ) : (
                                    <ChevronRight size={16} />
                                  )}
                                </button>
                              </td>
                              <td colSpan={3} className="px-6 py-4 whitespace-nowrap">
                                <strong className="text-primary-600-600">
                                  📁 {group.name} ({group.products.length} sản phẩm)
                                </strong>
                              </td>
                              <td className="px-6 py-4">
                                <button
                                  className="p-0 bg-transparent border-none"
                                  style={{
                                    color: group.is_favorite ? '#ffc107' : '#ffc107'
                                  }}
                                  onClick={() => handleToggleGroupFavorite(group.id, group.is_favorite)}
                                  title={group.is_favorite ? 'Bỏ yêu thích nhóm' : 'Đánh dấu yêu thích nhóm'}
                                >
                                  <Star
                                    size={20}
                                    fill={group.is_favorite ? '#ffc107' : 'none'}
                                    stroke="#ffc107"
                                    strokeWidth={2}
                                  />
                                </button>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {/* Cột trống cho actions - chỉ product mới có actions */}
                              </td>
                            </tr>

                            {/* Products Rows */}
                            {expandedGroups.has(group.id) && group.products.map(product => (
                              <tr key={product.id} className="hover:bg-gray-50">
                                <td className="py-4 whitespace-nowrap">
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap">
                                  <div className="ml-3">
                                    📄<strong className="text-gray-900">{product.name}</strong>
                                  </div>
                                </td>
                                <td className="px-6 py-4">
                                  <span className="text-gray-500">
                                    {product.description || 'Không có mô tả'}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`badge ${getStatusBadgeClass(product.status)}`}>
                                    {product.status}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  {/* Cột trống cho yêu thích - chỉ group mới có switch */}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="flex gap-1">
                                    <button
                                      className="btn btn-sm border border-primary-300 text-primary-600-600 hover:bg-primary-50"
                                      onClick={() => navigate(`/products/${product.id}`)}
                                      title="Xem chi tiết"
                                    >
                                      <Eye size={14} />
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </React.Fragment>
                        ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Pagination Info and Controls */}
          <div className="flex justify-between items-center mt-4">
            <PaginationInfo
              currentPage={filters.page || 1}
              totalPages={totalPages}
              totalItems={totalProducts}
              itemsPerPage={filters.limit || 20}
            />
            <PagingComponent
              currentPage={filters.page || 1}
              totalPages={totalPages}
              onPageChange={(page) => handleFilterChange('page', page)}
              maxVisiblePages={8}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default ProductsPage;

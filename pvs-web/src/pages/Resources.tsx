import React from 'react';
import { Database, Plus, Search, Filter, Server, HardDrive, Cpu } from 'lucide-react';

const ResourcesPage: React.FC = () => {
  const resources = [
    {
      id: 1,
      name: 'Server Database Chính',
      type: 'Database',
      status: 'active',
      usage: 75,
      location: 'Data Center A',
      lastUpdate: '2024-01-15 10:30:00'
    },
    {
      id: 2,
      name: 'Server Web Frontend',
      type: 'Web Server',
      status: 'active',
      usage: 45,
      location: 'Data Center B',
      lastUpdate: '2024-01-15 09:15:00'
    },
    {
      id: 3,
      name: 'Storage Backup',
      type: 'Storage',
      status: 'maintenance',
      usage: 90,
      location: 'Data Center A',
      lastUpdate: '2024-01-14 18:45:00'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="badge badge-success">Hoạt động</span>;
      case 'maintenance':
        return <span className="badge badge-warning">Bảo trì</span>;
      case 'inactive':
        return <span className="badge badge-error">Không hoạt động</span>;
      default:
        return <span className="badge badge-gray">Không xác đ<PERSON>nh</span>;
    }
  };

  const getUsageColor = (usage: number) => {
    if (usage >= 80) return 'danger';
    if (usage >= 60) return 'warning';
    return 'success';
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Database':
        return <Database size={18} className="mr-2" />;
      case 'Web Server':
        return <Server size={18} className="mr-2" />;
      case 'Storage':
        return <HardDrive size={18} className="mr-2" />;
      default:
        return <Cpu size={18} className="mr-2" />;
    }
  };

  return (
    <div className="w-full py-2">
      {/* Page Header */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-semibold mb-0 text-gray-800 flex items-center">
            <Database className="mr-2" size={24} />
            Danh sách nguồn lực
          </h1>
          <p className="text-gray-500 mb-0">Quản lý tài nguyên hệ thống và cơ sở hạ tầng</p>
        </div>
        <button className="btn btn-primary">
          <Plus size={18} className="mr-2" />
          Thêm nguồn lực
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="card mb-4">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={18} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Tìm kiếm nguồn lực..."
                />
              </div>
            </div>
            <div>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                <option value="">Tất cả loại</option>
                <option value="database">Database</option>
                <option value="server">Web Server</option>
                <option value="storage">Storage</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Resources Table */}
      <div className="card">
        <div className="card-header">
          <h5 className="text-lg font-semibold mb-0">Danh sách nguồn lực</h5>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="table">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên nguồn lực</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mức sử dụng</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vị trí</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cập nhật cuối</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {resources.map((resource) => (
                  <tr key={resource.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getTypeIcon(resource.type)}
                        <strong className="text-gray-900">{resource.name}</strong>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-gray-900">{resource.type}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(resource.status)}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            className={`h-2 rounded-full bg-${getUsageColor(resource.usage)}-600`}
                            style={{ width: `${resource.usage}%` }}
                          ></div>
                        </div>
                        <small className="text-gray-600">{resource.usage}%</small>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-gray-900">{resource.location}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <small className="text-gray-500">{resource.lastUpdate}</small>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex gap-1">
                        <button className="btn btn-sm border border-primary-300 text-primary-600-600 hover:bg-primary-50">Chi tiết</button>
                        <button className="btn btn-sm btn-secondary">Sửa</button>
                        <button className="btn btn-sm border border-error-300 text-error-600 hover:bg-error-50">Xóa</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Resource Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        <div>
          <div className="card text-center">
            <div className="p-6">
              <Server size={48} className="text-primary-600-600 mb-3 mx-auto" />
              <h5 className="text-lg font-semibold mb-2">Tổng số Server</h5>
              <h2 className="text-2xl font-bold text-primary-600 mb-2">12</h2>
              <p className="text-gray-500">8 hoạt động, 4 bảo trì</p>
            </div>
          </div>
        </div>
        <div>
          <div className="card text-center">
            <div className="p-6">
              <Database size={48} className="text-success-600-600 mb-3 mx-auto" />
              <h5 className="text-lg font-semibold mb-2">Database</h5>
              <h2 className="text-2xl font-bold text-success-600 mb-2">5</h2>
              <p className="text-gray-500">Tất cả đang hoạt động</p>
            </div>
          </div>
        </div>
        <div>
          <div className="card text-center">
            <div className="p-6">
              <HardDrive size={48} className="text-warning-600-600 mb-3 mx-auto" />
              <h5 className="text-lg font-semibold mb-2">Storage</h5>
              <h2 className="text-2xl font-bold text-warning-600 mb-2">8.5TB</h2>
              <p className="text-gray-500">Đã sử dụng 6.2TB</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourcesPage;

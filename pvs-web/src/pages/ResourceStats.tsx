import React from 'react';
import { BarChart3, Activity, Zap, Clock, TrendingUp, AlertTriangle } from 'lucide-react';

const ResourceStatsPage: React.FC = () => {
  return (
    <div className="w-full py-2">
      {/* Page Header */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-semibold mb-0 text-gray-800 flex items-center">
            <BarChart3 className="mr-2" size={24} />
            Thống kê nguồn lực
          </h1>
          <p className="text-gray-500 mb-0">Phân tích hiệu suất và sử dụng tài nguyên hệ thống</p>
        </div>
        <div className="flex gap-2">
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 w-auto">
            <option value="realtime">Thời gian thực</option>
            <option value="1h">1 giờ qua</option>
            <option value="24h">24 giờ qua</option>
            <option value="7d">7 ngày qua</option>
          </select>
          <button className="btn border border-primary-300 text-primary-600-600 hover:bg-primary-50">
            <Activity size={18} className="mr-2" />
            Làm mới
          </button>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
        <div>
          <div className="card shadow-md h-full py-2 border-l-4 border-primary-600">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1 mr-4">
                  <div className="text-xs font-bold text-primary-600 uppercase mb-1">
                    CPU Usage
                  </div>
                  <div className="text-xl font-bold text-gray-800 mb-2">65.2%</div>
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div className="bg-primary-600 h-1 rounded-full" style={{ width: '65.2%' }}></div>
                  </div>
                </div>
                <div>
                  <Zap className="text-primary-600-600" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="xl:col-span-3 md:col-span-6 mb-4">
          <div className="card shadow-md h-full py-2 border-l-4 border-success-600">
            <div className="p-6">
              <div className="grid grid-cols-12 gap-4 gap-0 items-center">
                <div className="col-span-12 mr-2">
                  <div className="text-xs font-bold text-success-600 text-uppercase mb-1">
                    Memory Usage
                  </div>
                  <div className="text-lg font-semibold mb-0 font-bold text-gray-800">78.5%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2" style={{ height: '4px' }}>
                    <div className="h-2 rounded-full badge-success" style={{ width: '78.5%' }}></div>
                  </div>
                </div>
                <div className="col-auto">
                  <Activity className="text-success-600-600" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="xl:col-span-3 md:col-span-6 mb-4">
          <div className="card shadow-md h-full py-2 border-l-4 border-blue-600">
            <div className="p-6">
              <div className="grid grid-cols-12 gap-4 gap-0 items-center">
                <div className="col-span-12 mr-2">
                  <div className="text-xs font-bold text-blue-600 text-uppercase mb-1">
                    Disk Usage
                  </div>
                  <div className="text-lg font-semibold mb-0 font-bold text-gray-800">45.8%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2" style={{ height: '4px' }}>
                    <div className="h-2 rounded-full badge-blue" style={{ width: '45.8%' }}></div>
                  </div>
                </div>
                <div className="col-auto">
                  <BarChart3 className="text-blue-600" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="xl:col-span-3 md:col-span-6 mb-4">
          <div className="card shadow-md h-full py-2 border-l-4 border-warning-600">
            <div className="p-6">
              <div className="grid grid-cols-12 gap-4 gap-0 items-center">
                <div className="col-span-12 mr-2">
                  <div className="text-xs font-bold text-warning-600 text-uppercase mb-1">
                    Network I/O
                  </div>
                  <div className="text-lg font-semibold mb-0 font-bold text-gray-800">1.2 GB/s</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2" style={{ height: '4px' }}>
                    <div className="h-2 rounded-full badge-warning" style={{ width: '60%' }}></div>
                  </div>
                </div>
                <div className="col-auto">
                  <TrendingUp className="text-warning-600-600" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Alerts */}
      <div className="grid grid-cols-12 gap-4">
        {/* Performance Chart */}
        <div className="xl:col-span-8 lg:col-span-7">
          <div className="card shadow-md mb-4">
            <div className="card-header py-3">
              <h6 className="m-0 font-bold text-primary-600-600">
                <Activity className="mr-2" size={20} />
                Biểu đồ hiệu suất hệ thống
              </h6>
            </div>
            <div className="p-6">
              <div className="chart-area">
                <div className="flex justify-center items-center" style={{ height: '300px' }}>
                  <p className="text-gray-500">Biểu đồ hiệu suất sẽ được hiển thị ở đây</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* System Alerts */}
        <div className="xl:col-span-4 lg:col-span-5">
          <div className="card shadow-md mb-4">
            <div className="card-header py-3">
              <h6 className="m-0 font-bold text-error-600">
                <AlertTriangle className="mr-2" size={20} />
                Cảnh báo hệ thống
              </h6>
            </div>
            <div className="p-6">
              <div className="space-y-2 space-y-0">
                <div className="flex justify-between items-center py-3 border-b border-gray-200 flex justify-between items-start">
                  <div className="ml-2 me-auto">
                    <div className="font-bold text-warning-600-600">High Memory Usage</div>
                    <small>Server DB-01 đang sử dụng 89% RAM</small>
                  </div>
                  <span className="badge badge-warning rounded-full">
                    <Clock size={12} />
                  </span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200 flex justify-between items-start">
                  <div className="ml-2 me-auto">
                    <div className="font-bold text-error-600">Disk Space Low</div>
                    <small>Storage-02 chỉ còn 5% dung lượng trống</small>
                  </div>
                  <span className="badge badge-error rounded-full">
                    <AlertTriangle size={12} />
                  </span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-200 flex justify-between items-start">
                  <div className="ml-2 me-auto">
                    <div className="font-bold text-blue-600">Network Latency</div>
                    <small>Độ trễ mạng tăng cao trong 30 phút qua</small>
                  </div>
                  <span className="badge badge-blue rounded-full">
                    <TrendingUp size={12} />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Resource Details Table */}
      <div className="card">
        <div className="card-header">
          <h5 className="text-lg font-semibold mb-0">Chi tiết sử dụng tài nguyên</h5>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="table hover:bg-gray-50">
              <thead>
                <tr>
                  <th>Tài nguyên</th>
                  <th>CPU (%)</th>
                  <th>Memory (%)</th>
                  <th>Disk (%)</th>
                  <th>Network (MB/s)</th>
                  <th>Uptime</th>
                  <th>Trạng thái</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>Server-DB-01</strong></td>
                  <td>
                    <span className="badge badge-warning">75%</span>
                  </td>
                  <td>
                    <span className="badge badge-error">89%</span>
                  </td>
                  <td>
                    <span className="badge badge-success">45%</span>
                  </td>
                  <td>125.6</td>
                  <td>15 ngày 4 giờ</td>
                  <td><span className="badge badge-success">Online</span></td>
                </tr>
                <tr>
                  <td><strong>Server-Web-01</strong></td>
                  <td>
                    <span className="badge badge-success">45%</span>
                  </td>
                  <td>
                    <span className="badge badge-warning">67%</span>
                  </td>
                  <td>
                    <span className="badge badge-success">32%</span>
                  </td>
                  <td>89.3</td>
                  <td>8 ngày 12 giờ</td>
                  <td><span className="badge badge-success">Online</span></td>
                </tr>
                <tr>
                  <td><strong>Storage-02</strong></td>
                  <td>
                    <span className="badge badge-success">12%</span>
                  </td>
                  <td>
                    <span className="badge badge-success">34%</span>
                  </td>
                  <td>
                    <span className="badge badge-error">95%</span>
                  </td>
                  <td>234.7</td>
                  <td>25 ngày 8 giờ</td>
                  <td><span className="badge badge-warning">Warning</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>


    </div>
  );
};

export default ResourceStatsPage;

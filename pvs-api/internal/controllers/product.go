package controllers

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductController struct {
	repo             repositories.ProductRepository
	productGroupRepo repositories.ProductGroupRepository
	componentRepo    repositories.ComponentRepository
	stageRepo        repositories.ProductStageRepository
}

func NewProductController(db *gorm.DB) *ProductController {
	return &ProductController{
		repo:             repositories.NewProductRepository(db),
		productGroupRepo: repositories.NewProductGroupRepository(db),
		componentRepo:    repositories.NewComponentRepository(db),
		stageRepo:        repositories.NewProductStageRepository(db),
	}
}

// ListProducts godoc
// @Summary      L<PERSON>y danh sách tất cả sản phẩm
// @Description  Trả về danh sách tất cả sản phẩm trong hệ thống
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200  {object}  utils.Response{data=[]dto.ProductResponse}
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /products [get]
func (c *ProductController) ListProducts(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	products, total, err := c.repo.List(ctx, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch products", err.Error())
		return
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Products retrieved successfully", products, total)
}

// ListGroupedProducts godoc
// @Summary      Lấy danh sách sản phẩm được nhóm theo group có phân trang
// @Description  Trả về danh sách các group và sản phẩm thuộc group đó, có thể lọc theo group, trạng thái và giai đoạn hiện tại
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page          query   integer  false  "Số trang"
// @Param        limit         query   integer  false  "Số lượng sản phẩm mỗi trang"
// @Param        group_id      query   integer  false  "ID của group cần lọc"
// @Param        year     	   query   integer  false  "Năm cần lọc"
// @Param        status        query   string   false  "Trạng thái cần lọc"
// @Param        is_favorite   query   boolean  false  "Lọc theo trạng thái yêu thích"
// @Success      200  {object}  utils.Response{data=dto.GroupedProductsResponse}
// @Failure      400  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/grouped [get]
func (c *ProductController) ListGroupedProducts(ctx *gin.Context) {
	params := utils.NewPagination(ctx)
	filters := make(map[string]interface{})

	// Parse group_id filter
	if groupIDStr := ctx.Query("group_id"); groupIDStr != "" {
		groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
		if err != nil {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid group ID", err.Error())
			return
		}
		filters["group_id"] = uint(groupID)
	}

	if status := ctx.Query("status"); status != "" {
		filters["status"] = status
	}

	if yearStr := ctx.Query("year"); yearStr != "" {
		year, err := strconv.ParseUint(yearStr, 10, 32)
		if err != nil {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid year", err.Error())
			return
		}
		filters["year"] = uint(year)
	}

	if isFavoriteStr := ctx.Query("is_favorite"); isFavoriteStr != "" {
		isFavorite, err := strconv.ParseBool(isFavoriteStr)
		if err != nil {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid is_favorite value, must be true or false", err.Error())
			return
		}
		filters["is_favorite"] = isFavorite
	}

	// Get grouped products with pagination
	response, err := c.repo.ListGroupedProducts(ctx, filters, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch grouped products", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Products retrieved successfully", response)
}

// SearchProducts godoc
// @Summary      Tìm kiếm sản phẩm
// @Description  Tìm kiếm sản phẩm theo tên và mô tả (tối thiểu 3 ký tự)
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        q      query     string  true   "Từ khóa tìm kiếm (tối thiểu 3 ký tự)"
// @Param        limit  query     int     false  "Số lượng kết quả tối đa (mặc định: 10)"
// @Success      200  {object}  utils.Response{data=[]dto.ProductResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /products/search [get]
func (c *ProductController) SearchProducts(ctx *gin.Context) {
	query := ctx.Query("q")
	if len(query) < 3 {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Search query must be at least 3 characters", "")
		return
	}

	// Parse limit parameter
	limit := 10 // default limit
	if limitStr := ctx.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 && parsedLimit <= 50 {
			limit = parsedLimit
		}
	}

	products, err := c.repo.Search(ctx, query, limit)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to search products", err.Error())
		return
	}

	// Convert to response DTOs
	var responses []dto.ProductResponse
	for _, product := range products {
		responses = append(responses, dto.FromProductModel(product))
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Products found successfully", responses)
}

// GetProductByID godoc
// @Summary      Lấy thông tin một sản phẩm
// @Description  Trả về thông tin chi tiết của một sản phẩm theo ID
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID sản phẩm"
// @Success      200  {object}  utils.Response{data=dto.ProductResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /products/{id} [get]
func (c *ProductController) GetProductByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	product, statusLogs, err := c.repo.GetDetailByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
		return
	}

	response := dto.FromProductModelWithDetails(product, statusLogs)
	utils.SuccessResponse(ctx, http.StatusOK, "Product retrieved successfully", response)
}

// CreateProduct godoc
// @Summary      Tạo sản phẩm mới
// @Description  Tạo một sản phẩm mới với thông tin được cung cấp
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        product  body      dto.ProductRequest  true  "Thông tin sản phẩm"
// @Success      201  {object}  utils.Response{data=dto.ProductResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /products [post]
func (c *ProductController) CreateProduct(ctx *gin.Context) {
	var request dto.ProductRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Chuyển đổi từ DTO sang model
	product := request.ToProductModel()

	err := c.repo.Create(ctx, product)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create product", err.Error())
		return
	}

	response := dto.FromProductModel(product)
	utils.SuccessResponse(ctx, http.StatusCreated, "Product created successfully", response)
}

// UpdateProduct godoc
// @Summary      Cập nhật sản phẩm
// @Description  Cập nhật thông tin của một sản phẩm theo ID
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id        path      int                 true  "ID sản phẩm"
// @Param        product   body      dto.ProductRequest  true  "Thông tin sản phẩm"
// @Success      200  {object}  utils.Response{data=dto.ProductResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id} [put]
func (c *ProductController) UpdateProduct(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem sản phẩm có tồn tại không
	exists, err := c.repo.ExistByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update product", err.Error())
		return
	}

	if !exists {
		err = errors.New("product not found")
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
		return
	}

	// Bind dữ liệu từ request
	var product models.Product
	if err := ctx.ShouldBindJSON(&product); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Validate trường name
	if product.Name == "" {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Product name is required", "")
		return
	}

	// Gán ID của sản phẩm hiện tại
	product.ID = uint(id)

	err = c.repo.Update(ctx, &product)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update product", err.Error())
		return
	}

	response := dto.FromProductModel(&product)
	utils.SuccessResponse(ctx, http.StatusOK, "Product updated successfully", response)
}

// DeleteProduct godoc
// @Summary      Xóa sản phẩm
// @Description  Xóa một sản phẩm theo ID
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID sản phẩm"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /products/{id} [delete]
func (c *ProductController) DeleteProduct(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem sản phẩm có tồn tại không
	exists, err := c.repo.ExistByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete product", err.Error())
		return
	}

	if !exists {
		err = errors.New("product not found")
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
		return
	}

	err = c.repo.Delete(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete product", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product deleted successfully", nil)
}

// ListProductGroups godoc
// @Summary      Lấy các nhóm sản phẩm liên quan
// @Description  Trả về danh sách các nhóm sản phẩm mà sản phẩm thuộc về
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID sản phẩm"
// @Success      200  {object}  utils.Response{data=dto.ProductDetailResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /products/{id}/product-groups [get]
func (c *ProductController) ListProductGroups(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	groups, err := c.repo.ListProductGroups(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product groups retrieved successfully", groups)
}

// ListComponents godoc
// @Summary      Lấy sản phẩm và các thành phần liên quan
// @Description  Trả về thông tin chi tiết của một sản phẩm và danh sách các thành phần hệ thống liên quan
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID sản phẩm"
// @Success      200  {array}   models.Component
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /products/{id}/components [get]
func (c *ProductController) ListComponents(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	product, err := c.repo.ListComponents(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product components retrieved successfully", product)
}

// GetProductWithStages godoc
// @Summary      Lấy thông tin các giai đoạn
// @Description  Trả về lịch sử các giai đoạn của sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID sản phẩm"
// @Success      200  {array}   models.ProductStage
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /products/{id}/stages [get]
func (c *ProductController) ListStages(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	stages, err := c.repo.ListStages(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Stage not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product stages retrieved successfully", stages)
}

// AddProductToGroup godoc
// @Summary      Thêm sản phẩm vào nhóm sản phẩm
// @Description  Thêm một sản phẩm vào một nhóm sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id        path      int  true  "ID sản phẩm"
// @Param        group_id  path      int  true  "ID nhóm sản phẩm"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/product-groups/{group_id} [post]
func (c *ProductController) AddProductToGroup(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product ID", err.Error())
		return
	}

	groupID, err := strconv.ParseUint(ctx.Param("group_id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid group ID", err.Error())
		return
	}

	// Kiểm tra sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(productID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add product to group", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Product not found", err.Error())
		return
	}

	// Kiểm tra nhóm sản phẩm tồn tại
	exists, err = c.productGroupRepo.ExistByID(ctx, uint(groupID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add product to group", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Product group not found", err.Error())
		return
	}

	// Kiểm tra xem quan hệ đã tồn tại chưa
	exists, err = c.repo.ExistsInGroup(ctx, uint(productID), uint(groupID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add product to group", err.Error())
		return
	}

	if exists {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Product already in this group", err.Error())
		return
	}

	// Thêm mối quan hệ
	err = c.repo.AddProductToGroup(ctx, uint(productID), uint(groupID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add product to group", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product added to group successfully", nil)
}

// RemoveProductFromGroup godoc
// @Summary      Xóa sản phẩm khỏi nhóm sản phẩm
// @Description  Xóa một sản phẩm khỏi một nhóm sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id        path      int  true  "ID sản phẩm"
// @Param        group_id  path      int  true  "ID nhóm sản phẩm"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/product-groups/{group_id} [delete]
func (c *ProductController) RemoveProductFromGroup(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product ID", err.Error())
		return
	}

	groupID, err := strconv.ParseUint(ctx.Param("group_id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid group ID", err.Error())
		return
	}

	// Kiểm tra xem quan hệ đã tồn tại chưa
	exists, err := c.repo.ExistsInGroup(ctx, uint(productID), uint(groupID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to remove product from group", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Product is not in this group", err.Error())
		return
	}

	// Xóa mối quan hệ
	err = c.repo.RemoveProductFromGroup(ctx, uint(productID), uint(groupID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to remove product from group", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product removed from group successfully", nil)
}

// AddComponentToProduct godoc
// @Summary      Thêm thành phần vào sản phẩm
// @Description  Thêm một thành phần hệ thống vào một sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int  true  "ID sản phẩm"
// @Param        component_id path      int  true  "ID thành phần"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/components/{component_id} [post]
func (c *ProductController) AddComponentToProduct(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product ID", err.Error())
		return
	}

	componentID, err := strconv.ParseUint(ctx.Param("component_id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid component ID", err.Error())
		return
	}

	// Kiểm tra sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(productID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add component to product", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Product not found", err.Error())
		return
	}

	// Kiểm tra thành phần tồn tại
	exists, err = c.componentRepo.ExistByID(ctx, uint(componentID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add component to product", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Component not found", err.Error())
		return
	}

	// Kiểm tra xem quan hệ đã tồn tại chưa
	exists, err = c.repo.HasComponent(ctx, uint(productID), uint(componentID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add component to product", err.Error())
		return
	}

	if exists {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Component already linked to this product", err.Error())
		return
	}

	// Thêm mối quan hệ
	err = c.repo.AddComponentToProduct(ctx, uint(productID), uint(componentID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add component to product", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Component added to product successfully", nil)
}

// RemoveComponentFromProduct godoc
// @Summary      Xóa thành phần khỏi sản phẩm
// @Description  Xóa một thành phần hệ thống khỏi một sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int  true  "ID sản phẩm"
// @Param        component_id path      int  true  "ID thành phần"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/components/{component_id} [delete]
func (c *ProductController) RemoveComponentFromProduct(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product ID", err.Error())
		return
	}

	componentID, err := strconv.ParseUint(ctx.Param("component_id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid component ID", err.Error())
		return
	}

	// Kiểm tra xem quan hệ có tồn tại không
	exists, err := c.repo.HasComponent(ctx, uint(productID), uint(componentID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to remove component from product", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Component is not linked to this product", err.Error())
		return
	}

	// Xóa mối quan hệ
	err = c.repo.RemoveComponentFromProduct(ctx, uint(productID), uint(componentID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to remove component from product", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Component removed from product successfully", nil)
}

// ChangeProductStatus godoc
// @Summary      Thay đổi trạng thái sản phẩm
// @Description  Thay đổi trạng thái active/inactive của sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int     true  "ID sản phẩm"
// @Param        status       query     string  true  "Trạng thái sản phẩm"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/status [patch]
func (c *ProductController) ChangeProductStatus(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	status := ctx.Query("status")
	if status == "" {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Status is required", err.Error())
		return
	}

	// Kiểm tra sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to change product status", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
		return
	}

	// Lấy thông tin người dùng từ context (nếu có)
	changedBy := "system"
	// TODO: Lấy thông tin người dùng từ context nếu cần
	// userInfo, exists := ctx.Get("user")
	// if exists {
	// 	changedBy = userInfo.(string)
	// }

	// Ghi chú cho việc thay đổi trạng thái
	note := ctx.Query("note")
	if note == "" {
		note = fmt.Sprintf("Changed status to %s", status)
	}

	// Cập nhật trạng thái và tạo log
	err = c.repo.UpdateStatus(ctx, uint(id), status, changedBy, note)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to change product status", err.Error())
		return
	}

	message := fmt.Sprintf("Product status changed to [%s] successfully", status)

	utils.SuccessResponse(ctx, http.StatusOK, message, nil)
}

// ToggleProductFavorite godoc
// @Summary      Toggle trạng thái yêu thích của sản phẩm
// @Description  Cập nhật trạng thái yêu thích (favorite) của một sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int  true  "ID sản phẩm"
// @Param        is_favorite  query     bool true  "Trạng thái yêu thích (true/false)"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/favorite [patch]
func (c *ProductController) ToggleProductFavorite(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	isFavoriteStr := ctx.Query("is_favorite")
	if isFavoriteStr == "" {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "is_favorite parameter is required", "")
		return
	}

	isFavorite, err := strconv.ParseBool(isFavoriteStr)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid is_favorite value, must be true or false", err.Error())
		return
	}

	// Kiểm tra sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to toggle product favorite", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", "")
		return
	}

	// Cập nhật trạng thái favorite
	err = c.repo.UpdateFavoriteStatus(ctx, uint(id), isFavorite)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to toggle product favorite", err.Error())
		return
	}

	var message string
	if isFavorite {
		message = "Product marked as favorite successfully"
	} else {
		message = "Product unmarked as favorite successfully"
	}

	utils.SuccessResponse(ctx, http.StatusOK, message, nil)
}

// GetLatestStage godoc
// @Summary      Lấy giai đoạn hiện tại của sản phẩm
// @Description  Trả về giai đoạn hiện tại cùng của sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int  true  "ID sản phẩm"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/stage [get]
func (c *ProductController) GetLatestStage(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(productID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to get latest stage", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Product not found", err.Error())
		return
	}

	// Lấy trạng thái mới nhất
	lastStage, err := c.stageRepo.FindLatestByProductID(ctx, uint(productID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to get latest stage", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Latest stage retrieved successfully", lastStage)
}

// ListProductStatusLogs godoc
// @Summary      Lấy lịch sử trạng thái sản phẩm
// @Description  Trả về lịch sử các thay đổi trạng thái của sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id      path      int  true  "ID sản phẩm"
// @Success      200  {array}   models.ProductStatusLog
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/status-logs [get]
func (c *ProductController) ListProductStatusLogs(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(productID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to list product status logs", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
		return
	}

	// Lấy danh sách lịch sử trạng thái
	logs, err := c.repo.ListStatusLogs(ctx, uint(productID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to list product status logs", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product status logs retrieved successfully", logs)
}

// SetProductStage godoc
// @Summary      Thiết lập giai đoạn mới cho sản phẩm
// @Description  Thiết lập giai đoạn mới cho sản phẩm và cập nhật giai đoạn hiện tại của sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id      path      int                      true  "ID sản phẩm"
// @Param        stage   body      dto.SetProductStageRequest  true  "Thông tin giai đoạn mới"
// @Success      200  {object}  models.ProductStage
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/set-stage [post]
func (c *ProductController) SetProductStage(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(productID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to set product stage", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
		return
	}

	// Bind dữ liệu từ request
	var request dto.SetProductStageRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Thiết lập giai đoạn mới cho sản phẩm
	newStage, err := c.repo.SetProductStage(ctx, uint(productID), request.StageCode, request.StartDate, request.Notes)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to set product stage", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product stage set successfully", newStage)
}

// AddProductStatusLog godoc
// @Summary      Thêm status log cho sản phẩm
// @Description  Thêm một status log mới cho sản phẩm
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int                           true  "ID sản phẩm"
// @Param        status_log   body      dto.AddStatusLogRequest       true  "Thông tin status log"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/status-logs [post]
func (c *ProductController) AddProductStatusLog(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product ID", err.Error())
		return
	}

	var request dto.AddStatusLogRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Kiểm tra sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(productID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to check product", err.Error())
		return
	}
	if !exists {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", "")
		return
	}

	// Tạo status log
	statusLog := &models.ProductStatusLog{
		ProductID: uint(productID),
		Status:    request.Status,
		ChangedBy: request.ChangedBy,
		ChangedAt: time.Now(),
		Note:      request.Note,
	}

	err = c.repo.CreateStatusLog(ctx, statusLog)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add status log", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Status log added successfully", statusLog)
}

// AddComponentToProductByRequest godoc
// @Summary      Thêm component vào sản phẩm qua request body
// @Description  Thêm một component vào sản phẩm sử dụng request body
// @Tags         Products
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int                           true  "ID sản phẩm"
// @Param        component    body      dto.AddComponentRequest       true  "Thông tin component"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /products/{id}/components [post]
func (c *ProductController) AddComponentToProductByRequest(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product ID", err.Error())
		return
	}

	var request dto.AddComponentRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Kiểm tra sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(productID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to check product", err.Error())
		return
	}
	if !exists {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", "")
		return
	}

	// Kiểm tra component tồn tại
	exists, err = c.componentRepo.ExistByID(ctx, request.ComponentID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to check component", err.Error())
		return
	}
	if !exists {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Component not found", "")
		return
	}

	// Kiểm tra component đã được thêm vào sản phẩm chưa
	hasComponent, err := c.repo.HasComponent(ctx, uint(productID), request.ComponentID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to check component", err.Error())
		return
	}
	if hasComponent {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Component already added to product", "")
		return
	}

	// Thêm component vào sản phẩm
	err = c.repo.AddComponentToProduct(ctx, uint(productID), request.ComponentID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add component to product", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Component added to product successfully", nil)
}

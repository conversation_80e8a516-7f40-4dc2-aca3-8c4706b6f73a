basePath: /api
definitions:
  gorm.DeletedAt:
    properties:
      time:
        type: string
      valid:
        description: Valid is true if Time is not NULL
        type: boolean
    type: object
  internal_controllers.MultiProductStatsResponse:
    properties:
      datasets:
        description: Dữ liệu của từng sản phẩm
        items:
          $ref: '#/definitions/internal_controllers.ProductDataset'
        type: array
      labels:
        description: Các mốc thời gian
        items:
          type: string
        type: array
    type: object
  internal_controllers.ProductDataset:
    properties:
      data:
        description: Số liệu theo thời gian
        items:
          type: integer
        type: array
      product_id:
        type: integer
      product_name:
        type: string
    type: object
  pvs-api_internal_dto.AddComponentRequest:
    properties:
      component_id:
        example: 1
        type: integer
    required:
    - component_id
    type: object
  pvs-api_internal_dto.AddStatusLogRequest:
    properties:
      changed_by:
        example: admin
        type: string
      note:
        example: Cập nhật trạng thái sản phẩm
        type: string
      status:
        example: ACTIVE
        type: string
    required:
    - changed_by
    - status
    type: object
  pvs-api_internal_dto.AuthResponse:
    properties:
      token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      user:
        $ref: '#/definitions/pvs-api_internal_dto.UserInfo'
    type: object
  pvs-api_internal_dto.BrandIdentityRequest:
    properties:
      color_code:
        example: '#FF0000'
        type: string
      company_name:
        example: Acme Corp
        type: string
      effective_at:
        example: "2024-01-01T00:00:00Z"
        type: string
      expired_at:
        example: "2024-12-31T23:59:59Z"
        type: string
      logo_url:
        example: https://example.com/logo.png
        type: string
      note:
        example: Rebranding for 2024
        type: string
    required:
    - color_code
    - company_name
    - effective_at
    type: object
  pvs-api_internal_dto.BrandIdentityResponse:
    properties:
      color_code:
        example: '#FF0000'
        type: string
      company_name:
        example: Acme Corp
        type: string
      created_at:
        type: string
      effective_at:
        example: "2024-01-01T00:00:00Z"
        type: string
      expired_at:
        example: "2024-12-31T23:59:59Z"
        type: string
      id:
        example: 1
        type: integer
      is_active:
        example: true
        type: boolean
      logo_url:
        example: https://example.com/logo.png
        type: string
      note:
        example: Rebranding for 2024
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_dto.ChangePasswordRequest:
    properties:
      new_password:
        minLength: 6
        type: string
      old_password:
        type: string
    required:
    - new_password
    - old_password
    type: object
  pvs-api_internal_dto.ComponentResponse:
    properties:
      component_type_code:
        example: APPLICATION
        type: string
      confluence_url:
        example: https://confluence.example.com/pages/component/123
        type: string
      created_at:
        type: string
      description:
        example: Dịch vụ xác thực người dùng
        type: string
      extra_function:
        type: string
      id:
        example: 1
        type: integer
      importance:
        type: string
      main_function:
        type: string
      name:
        example: Authentication Service
        type: string
      parent_component_id:
        example: 0
        type: integer
      target_user_code:
        example: CUSTOMER
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_dto.CreateUserRequest:
    properties:
      full_name:
        type: string
      password:
        minLength: 6
        type: string
      role_code:
        type: string
      username:
        type: string
    required:
    - full_name
    - password
    - role_code
    - username
    type: object
  pvs-api_internal_dto.DropdownItem:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: Example Item
        type: string
    type: object
  pvs-api_internal_dto.GroupWithProducts:
    properties:
      description:
        type: string
      id:
        type: integer
      is_favorite:
        type: boolean
      name:
        type: string
      products:
        items:
          $ref: '#/definitions/pvs-api_internal_dto.ProductInGroup'
        type: array
    type: object
  pvs-api_internal_dto.GroupedProductsResponse:
    properties:
      count:
        description: Số lượng sản phẩm trong trang hiện tại
        type: integer
      groups:
        items:
          $ref: '#/definitions/pvs-api_internal_dto.GroupWithProducts'
        type: array
      total:
        description: Tổng số sản phẩm thỏa mãn điều kiện
        type: integer
    type: object
  pvs-api_internal_dto.LoginRequest:
    properties:
      password:
        example: Admin@123
        type: string
      username:
        example: admin
        type: string
    required:
    - password
    - username
    type: object
  pvs-api_internal_dto.NotableSystemRequest:
    properties:
      description:
        example: Hệ thống ngân hàng lõi được triển khai cho nhiều ngân hàng lớn
        type: string
      end_year:
        example: 2023
        type: integer
      name:
        example: Core Banking System
        type: string
      start_year:
        example: 2020
        type: integer
    required:
    - name
    - start_year
    type: object
  pvs-api_internal_dto.NotableSystemResponse:
    properties:
      description:
        example: Hệ thống ngân hàng lõi được triển khai cho nhiều ngân hàng lớn
        type: string
      end_year:
        example: 2023
        type: integer
      id:
        example: 1
        type: integer
      name:
        example: Core Banking System
        type: string
      start_year:
        example: 2020
        type: integer
    type: object
  pvs-api_internal_dto.ProductDetailResponse:
    properties:
      components:
        items:
          $ref: '#/definitions/pvs-api_internal_models.Component'
        type: array
      created_at:
        type: string
      description:
        example: Ứng dụng ngân hàng di động
        type: string
      id:
        example: 1
        type: integer
      is_favorite:
        example: false
        type: boolean
      name:
        example: Banking App
        type: string
      status:
        example: ACTIVE
        type: string
      status_logs:
        items:
          $ref: '#/definitions/pvs-api_internal_models.ProductStatusLog'
        type: array
      updated_at:
        type: string
    type: object
  pvs-api_internal_dto.ProductGroupRequest:
    properties:
      description:
        example: Nhóm các ứng dụng di động
        type: string
      name:
        example: Mobile Apps
        type: string
    required:
    - name
    type: object
  pvs-api_internal_dto.ProductInGroup:
    properties:
      current_stage:
        type: string
      description:
        type: string
      has_usage_data:
        type: boolean
      id:
        type: integer
      is_favorite:
        type: boolean
      name:
        type: string
      status:
        type: string
    type: object
  pvs-api_internal_dto.ProductJourneyListResponse:
    properties:
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      name:
        type: string
      product_id:
        type: integer
      product_name:
        type: string
      status:
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_dto.ProductJourneyRequest:
    properties:
      description:
        example: Hành trình khách hàng mở tài khoản ngân hàng
        type: string
      flow_data:
        example: '{"components": {...}, "actions": {...}, "steps": [...]}'
        type: string
      name:
        example: Hành trình mở tài khoản
        type: string
      product_id:
        example: 1
        type: integer
      status:
        example: ACTIVE
        type: string
    required:
    - name
    - product_id
    type: object
  pvs-api_internal_dto.ProductJourneyResponse:
    properties:
      created_at:
        type: string
      description:
        type: string
      flow_data:
        type: string
      id:
        type: integer
      name:
        type: string
      product_id:
        type: integer
      product_name:
        type: string
      status:
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_dto.ProductRequest:
    properties:
      description:
        example: Ứng dụng ngân hàng di động
        type: string
      name:
        example: Banking App
        type: string
      status:
        example: ACTIVE
        type: string
    required:
    - name
    type: object
  pvs-api_internal_dto.ProductResponse:
    properties:
      created_at:
        type: string
      description:
        example: Ứng dụng ngân hàng di động
        type: string
      id:
        example: 1
        type: integer
      is_favorite:
        example: false
        type: boolean
      name:
        example: Banking App
        type: string
      status:
        example: ACTIVE
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_dto.ProductStageRequest:
    properties:
      end_date:
        example: "2023-02-01T00:00:00Z"
        type: string
      notes:
        example: Hoàn thành giai đoạn nghiên cứu ý tưởng
        type: string
      product_id:
        example: 1
        type: integer
      stage_code:
        example: IDEA
        type: string
      start_date:
        example: "2023-01-01T00:00:00Z"
        type: string
    required:
    - product_id
    - stage_code
    - start_date
    type: object
  pvs-api_internal_dto.ProductStageStakeholderRequest:
    properties:
      role_code:
        example: PRIMARY
        type: string
      stakeholder_id:
        type: integer
      tasks:
        example: Phụ trách quản lý tiến độ sprint
        type: string
    required:
    - role_code
    - stakeholder_id
    type: object
  pvs-api_internal_dto.RefreshTokenRequest:
    properties:
      token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    required:
    - token
    type: object
  pvs-api_internal_dto.RoleRequest:
    properties:
      code:
        example: PRODUCT_MANAGER
        type: string
      description:
        example: Manages product lifecycle
        type: string
      name:
        example: Product Manager
        type: string
      permissions:
        example:
        - '[''product:create'''
        - '''product:update'']'
        items:
          type: string
        type: array
    required:
    - code
    - name
    type: object
  pvs-api_internal_dto.RoleResponse:
    properties:
      code:
        example: PRODUCT_MANAGER
        type: string
      description:
        example: Manages product lifecycle
        type: string
      is_system:
        example: false
        type: boolean
      name:
        example: Product Manager
        type: string
      permissions:
        example:
        - '[''product:create'''
        - '''product:update'']'
        items:
          type: string
        type: array
    type: object
  pvs-api_internal_dto.SetProductStageRequest:
    properties:
      notes:
        example: Chuyển sang giai đoạn phát triển
        type: string
      stage_code:
        example: DEVELOPMENT
        type: string
      start_date:
        type: string
    required:
    - stage_code
    - start_date
    type: object
  pvs-api_internal_dto.StakeholderRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      full_name:
        example: Nguyễn Văn A
        type: string
      position:
        type: string
    required:
    - email
    - full_name
    type: object
  pvs-api_internal_dto.StakeholderResponse:
    properties:
      created_at:
        type: string
      email:
        example: <EMAIL>
        type: string
      full_name:
        example: Nguyễn Văn A
        type: string
      id:
        example: 1
        type: integer
      updated_at:
        type: string
    type: object
  pvs-api_internal_dto.SystemParamRequest:
    properties:
      code:
        example: ACTIVE
        type: string
      display_order:
        example: 1
        type: integer
      is_active:
        example: true
        type: boolean
      name:
        example: Đang hoạt động
        type: string
      param_type:
        allOf:
        - $ref: '#/definitions/pvs-api_internal_models.ParamType'
        example: PRODUCT_STATUS
    required:
    - code
    - name
    - param_type
    type: object
  pvs-api_internal_dto.SystemParamResponse:
    properties:
      code:
        example: ACTIVE
        type: string
      created_at:
        type: string
      display_order:
        example: 1
        type: integer
      id:
        example: 1
        type: integer
      is_active:
        example: true
        type: boolean
      name:
        example: Đang hoạt động
        type: string
      param_type:
        allOf:
        - $ref: '#/definitions/pvs-api_internal_models.ParamType'
        example: PRODUCT_STATUS
      updated_at:
        type: string
    type: object
  pvs-api_internal_dto.TimelineComponentInfo:
    properties:
      link:
        type: string
      name:
        type: string
    type: object
  pvs-api_internal_dto.TimelineGroup:
    properties:
      id:
        type: integer
      name:
        type: string
      products:
        items:
          $ref: '#/definitions/pvs-api_internal_dto.TimelineProduct'
        type: array
    type: object
  pvs-api_internal_dto.TimelineProduct:
    properties:
      components:
        items:
          $ref: '#/definitions/pvs-api_internal_dto.TimelineComponentInfo'
        type: array
      end_year:
        type: integer
      id:
        type: integer
      logs:
        items:
          $ref: '#/definitions/pvs-api_internal_dto.TimelineStatusLog'
        type: array
      name:
        type: string
      stages:
        items:
          $ref: '#/definitions/pvs-api_internal_dto.TimelineStage'
        type: array
      start_year:
        type: integer
    type: object
  pvs-api_internal_dto.TimelineResponse:
    properties:
      end_year:
        description: Latest year across all products
        type: integer
      groups:
        items:
          $ref: '#/definitions/pvs-api_internal_dto.TimelineGroup'
        type: array
      start_year:
        description: Earliest year across all products
        type: integer
    type: object
  pvs-api_internal_dto.TimelineStage:
    properties:
      end_year:
        description: Optional end year
        type: integer
      name:
        type: string
      start_year:
        type: integer
    type: object
  pvs-api_internal_dto.TimelineStatusLog:
    properties:
      end_year:
        type: integer
      start_year:
        type: integer
      status:
        type: string
    type: object
  pvs-api_internal_dto.UpdateRolePermissionsRequest:
    properties:
      permissions:
        items:
          type: string
        type: array
    required:
    - permissions
    type: object
  pvs-api_internal_dto.UpdateUserRequest:
    properties:
      full_name:
        type: string
      is_active:
        type: boolean
    required:
    - full_name
    type: object
  pvs-api_internal_dto.UpdateUserRoleRequest:
    properties:
      role_code:
        type: string
    required:
    - role_code
    type: object
  pvs-api_internal_dto.UserInfo:
    properties:
      id:
        example: 1
        type: integer
      permissions:
        example:
        - '[''user:list'''
        - ' ''user:create'']'
        items:
          type: string
        type: array
      role_code:
        example: ADMIN
        type: string
      username:
        example: admin
        type: string
    type: object
  pvs-api_internal_models.Component:
    properties:
      child_components:
        items:
          $ref: '#/definitions/pvs-api_internal_models.Component'
        type: array
      color_code:
        type: string
      component_type_code:
        type: string
      confluence_url:
        type: string
      created_at:
        type: string
      deleted_at:
        $ref: '#/definitions/gorm.DeletedAt'
      description:
        type: string
      extra_function:
        type: string
      id:
        type: integer
      importance:
        type: string
      main_function:
        type: string
      name:
        type: string
      parent_component:
        $ref: '#/definitions/pvs-api_internal_models.Component'
      parent_component_id:
        type: integer
      products:
        items:
          $ref: '#/definitions/pvs-api_internal_models.Product'
        type: array
      target_user_code:
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_models.ParamType:
    enum:
    - COMPONENT_TYPE
    - TARGET_USER
    - STAGE_TYPE
    - PRODUCT_STATUS
    - STAKEHOLDER_ROLE
    type: string
    x-enum-comments:
      ParamTypeComponentType: Loại hệ thống/thành phần
      ParamTypeProductStatus: Trạng thái của sản phẩm
      ParamTypeStageType: Giai đoạn của sản phẩm
      ParamTypeStakeholderRole: Vai trò của người tham gia sản phẩm
      ParamTypeTargetUser: Nhóm người dùng sử dụng hệ thống
    x-enum-varnames:
    - ParamTypeComponentType
    - ParamTypeTargetUser
    - ParamTypeStageType
    - ParamTypeProductStatus
    - ParamTypeStakeholderRole
  pvs-api_internal_models.Product:
    properties:
      components:
        items:
          $ref: '#/definitions/pvs-api_internal_models.Component'
        type: array
      created_at:
        type: string
      current_stage:
        $ref: '#/definitions/pvs-api_internal_models.ProductStage'
      deleted_at:
        $ref: '#/definitions/gorm.DeletedAt'
      description:
        type: string
      id:
        type: integer
      is_favorite:
        type: boolean
      name:
        type: string
      product_groups:
        items:
          $ref: '#/definitions/pvs-api_internal_models.ProductGroup'
        type: array
      product_stages:
        items:
          $ref: '#/definitions/pvs-api_internal_models.ProductStage'
        type: array
      stage_id:
        type: integer
      status:
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_models.ProductGroup:
    properties:
      created_at:
        type: string
      deleted_at:
        $ref: '#/definitions/gorm.DeletedAt'
      description:
        type: string
      id:
        type: integer
      is_favorite:
        type: boolean
      name:
        type: string
      products:
        items:
          $ref: '#/definitions/pvs-api_internal_models.Product'
        type: array
      updated_at:
        type: string
    type: object
  pvs-api_internal_models.ProductStage:
    properties:
      created_at:
        type: string
      deleted_at:
        $ref: '#/definitions/gorm.DeletedAt'
      end_date:
        type: string
      id:
        type: integer
      notes:
        type: string
      product:
        $ref: '#/definitions/pvs-api_internal_models.Product'
      product_id:
        type: integer
      stage_code:
        type: string
      stakeholders:
        items:
          $ref: '#/definitions/pvs-api_internal_models.ProductStageStakeholder'
        type: array
      start_date:
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_models.ProductStageStakeholder:
    properties:
      created_at:
        type: string
      deleted_at:
        $ref: '#/definitions/gorm.DeletedAt'
      product_stage:
        $ref: '#/definitions/pvs-api_internal_models.ProductStage'
      product_stage_id:
        type: integer
      role_code:
        type: string
      stakeholder:
        $ref: '#/definitions/pvs-api_internal_models.Stakeholder'
      stakeholder_id:
        type: integer
      tasks:
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_models.ProductStatusLog:
    properties:
      changed_at:
        type: string
      changed_by:
        type: string
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      product:
        $ref: '#/definitions/pvs-api_internal_models.Product'
      product_id:
        type: integer
      status:
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_models.ProductUsageStat:
    properties:
      created_at:
        type: string
      id:
        type: integer
      month:
        description: Chỉ lưu năm-tháng
        type: string
      product:
        $ref: '#/definitions/pvs-api_internal_models.Product'
      product_id:
        type: integer
      updated_at:
        type: string
      users:
        type: integer
    type: object
  pvs-api_internal_models.Role:
    properties:
      code:
        type: string
      created_at:
        type: string
      deleted_at:
        $ref: '#/definitions/gorm.DeletedAt'
      description:
        type: string
      is_system:
        description: Đánh dấu role hệ thống, không được xóa
        type: boolean
      name:
        type: string
      permissions:
        description: Mảng các permission codes
        items:
          type: string
        type: array
      updated_at:
        type: string
      users:
        description: Relations
        items:
          $ref: '#/definitions/pvs-api_internal_models.User'
        type: array
    type: object
  pvs-api_internal_models.Stakeholder:
    properties:
      created_at:
        type: string
      deleted_at:
        $ref: '#/definitions/gorm.DeletedAt'
      email:
        type: string
      full_name:
        type: string
      id:
        type: integer
      position:
        type: string
      updated_at:
        type: string
    type: object
  pvs-api_internal_models.User:
    properties:
      created_at:
        type: string
      deleted_at:
        $ref: '#/definitions/gorm.DeletedAt'
      full_name:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      last_login:
        type: string
      role:
        allOf:
        - $ref: '#/definitions/pvs-api_internal_models.Role'
        description: Relations
      role_code:
        type: string
      updated_at:
        type: string
      username:
        type: string
    type: object
  pvs-api_pkg_utils.Response:
    properties:
      count:
        type: integer
      data: {}
      error: {}
      message:
        type: string
      success:
        type: boolean
      total:
        type: integer
    type: object
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: API cho Hệ thống Quản lý Sản phẩm sử dụng Gin và GORM
  title: Hệ thống Quản lý Sản phẩm API
  version: "1.0"
paths:
  /brand-identities:
    get:
      consumes:
      - application/json
      description: Trả về danh sách các lần thay đổi nhận diện thương hiệu
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy lịch sử thay đổi nhận diện thương hiệu
      tags:
      - Brand-Identity
    post:
      consumes:
      - application/json
      description: Tạo một nhận diện thương hiệu mới và tự động cập nhật thời gian
        hết hiệu lực của nhận diện cũ
      parameters:
      - description: Thông tin nhận diện thương hiệu
        in: body
        name: identity
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.BrandIdentityRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.BrandIdentityResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo nhận diện thương hiệu mới
      tags:
      - Brand-Identity
  /brand-identities/{id}:
    delete:
      consumes:
      - application/json
      description: Xóa một nhận diện thương hiệu khỏi hệ thống
      parameters:
      - description: ID nhận diện thương hiệu
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa nhận diện thương hiệu
      tags:
      - Brand-Identity
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một nhận diện thương hiệu theo ID
      parameters:
      - description: ID nhận diện thương hiệu
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.BrandIdentityResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy chi tiết một nhận diện thương hiệu
      tags:
      - Brand-Identity
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một nhận diện thương hiệu
      parameters:
      - description: ID nhận diện thương hiệu
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin nhận diện thương hiệu
        in: body
        name: identity
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.BrandIdentityRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.BrandIdentityResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật nhận diện thương hiệu
      tags:
      - Brand-Identity
  /brand-identities/active:
    get:
      consumes:
      - application/json
      description: Trả về thông tin nhận diện thương hiệu đang có hiệu lực
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.BrandIdentityResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin nhận diện thương hiệu hiện tại
      tags:
      - Brand-Identity
  /components:
    get:
      consumes:
      - application/json
      description: Trả về danh sách tất cả thành phần hệ thống có phân trang
      parameters:
      - description: Số trang
        in: query
        name: page
        type: integer
      - description: Số lượng mỗi trang
        in: query
        name: limit
        type: integer
      - description: Lọc theo parent ID
        in: query
        name: parent_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_dto.ComponentResponse'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách tất cả thành phần hệ thống
      tags:
      - Components
    post:
      consumes:
      - application/json
      description: Tạo một thành phần hệ thống mới với thông tin được cung cấp
      parameters:
      - description: Thông tin thành phần
        in: body
        name: component
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_models.Component'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/pvs-api_internal_models.Component'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo thành phần hệ thống mới
      tags:
      - Components
  /components/{id}:
    delete:
      consumes:
      - application/json
      description: Xóa một thành phần hệ thống theo ID
      parameters:
      - description: ID thành phần
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa thành phần hệ thống
      tags:
      - Components
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một thành phần hệ thống theo ID
      parameters:
      - description: ID thành phần
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_models.Component'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin một thành phần hệ thống
      tags:
      - Components
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một thành phần hệ thống theo ID
      parameters:
      - description: ID thành phần
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin thành phần
        in: body
        name: component
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_models.Component'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_models.Component'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật thành phần hệ thống
      tags:
      - Components
  /components/{id}/children:
    get:
      consumes:
      - application/json
      description: Trả về danh sách các thành phần con của một thành phần hệ thống
      parameters:
      - description: ID thành phần cha
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/pvs-api_internal_models.Component'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách các thành phần con
      tags:
      - Components
  /components/dropdown:
    get:
      consumes:
      - application/json
      description: Trả về danh sách id và tên của components, có thể lọc theo từ khóa
      parameters:
      - description: Search keyword
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_dto.DropdownItem'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách component cho dropdown
      tags:
      - Components
  /login:
    post:
      consumes:
      - application/json
      description: Xác thực người dùng và trả về JWT token
      parameters:
      - description: Thông tin đăng nhập
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Đăng nhập thành công
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.AuthResponse'
              type: object
        "400":
          description: Lỗi dữ liệu đầu vào
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Thông tin đăng nhập không hợp lệ
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Lỗi server
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      summary: Đăng nhập vào hệ thống
      tags:
      - Authentication
  /notable-systems:
    get:
      consumes:
      - application/json
      description: Trả về danh sách các hệ thống nổi bật đã triển khai
      parameters:
      - description: Lọc theo năm
        in: query
        name: year
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_dto.NotableSystemResponse'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách các hệ thống nổi bật
      tags:
      - Notable-Systems
    post:
      consumes:
      - application/json
      description: Tạo một hệ thống nổi bật mới
      parameters:
      - description: Thông tin hệ thống
        in: body
        name: system
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.NotableSystemRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.NotableSystemResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo hệ thống nổi bật mới
      tags:
      - Notable-Systems
  /notable-systems/{id}:
    delete:
      consumes:
      - application/json
      description: Xóa một hệ thống nổi bật khỏi hệ thống
      parameters:
      - description: ID hệ thống
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa hệ thống nổi bật
      tags:
      - Notable-Systems
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một hệ thống nổi bật theo ID
      parameters:
      - description: ID hệ thống
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.NotableSystemResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy chi tiết một hệ thống nổi bật
      tags:
      - Notable-Systems
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một hệ thống nổi bật
      parameters:
      - description: ID hệ thống
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin hệ thống
        in: body
        name: system
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.NotableSystemRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.NotableSystemResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật hệ thống nổi bật
      tags:
      - Notable-Systems
  /product-groups:
    get:
      consumes:
      - application/json
      description: Trả về danh sách tất cả nhóm sản phẩm trong hệ thống
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách tất cả nhóm sản phẩm
      tags:
      - Product-Groups
    post:
      consumes:
      - application/json
      description: Tạo một nhóm sản phẩm mới với thông tin được cung cấp
      parameters:
      - description: Thông tin nhóm sản phẩm
        in: body
        name: productGroup
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.ProductGroupRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/pvs-api_internal_models.ProductGroup'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo nhóm sản phẩm mới
      tags:
      - Product-Groups
  /product-groups/{id}:
    delete:
      consumes:
      - application/json
      description: Xóa một nhóm sản phẩm theo ID
      parameters:
      - description: ID nhóm sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa nhóm sản phẩm
      tags:
      - Product-Groups
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một nhóm sản phẩm theo ID
      parameters:
      - description: ID nhóm sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_models.ProductGroup'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin một nhóm sản phẩm
      tags:
      - Product-Groups
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một nhóm sản phẩm theo ID
      parameters:
      - description: ID nhóm sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin nhóm sản phẩm
        in: body
        name: productGroup
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_models.ProductGroup'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_models.ProductGroup'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật nhóm sản phẩm
      tags:
      - Product-Groups
  /product-groups/{id}/favorite:
    patch:
      consumes:
      - application/json
      description: Cập nhật trạng thái yêu thích (favorite) của một nhóm sản phẩm
      parameters:
      - description: ID nhóm sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Trạng thái yêu thích (true/false)
        in: query
        name: favorite
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Toggle trạng thái yêu thích của nhóm sản phẩm
      tags:
      - Product-Groups
  /product-groups/{id}/products:
    get:
      consumes:
      - application/json
      description: Trả về danh sách các sản phẩm thuộc nhóm sản phẩm
      parameters:
      - description: ID nhóm sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/pvs-api_internal_models.Product'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy các sản phẩm liên quan nhóm sản phẩm
      tags:
      - Product-Groups
  /product-groups/dropdown:
    get:
      consumes:
      - application/json
      description: Trả về danh sách id và tên của product groups, có thể lọc theo
        từ khóa
      parameters:
      - description: Search keyword
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_dto.DropdownItem'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách product group cho dropdown
      tags:
      - Product-Groups
  /product-journeys:
    get:
      consumes:
      - application/json
      description: Trả về danh sách hành trình sản phẩm với phân trang và filter
      parameters:
      - description: Số trang
        in: query
        name: page
        type: integer
      - description: Số lượng mỗi trang
        in: query
        name: limit
        type: integer
      - description: ID sản phẩm
        in: query
        name: product_id
        type: integer
      - description: Trạng thái
        in: query
        name: status
        type: string
      - description: Tìm kiếm theo tên hoặc mô tả
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_dto.ProductJourneyListResponse'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách hành trình sản phẩm
      tags:
      - Product Journeys
    post:
      consumes:
      - application/json
      description: Tạo một hành trình sản phẩm mới với thông tin được cung cấp
      parameters:
      - description: Thông tin hành trình
        in: body
        name: journey
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.ProductJourneyRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.ProductJourneyResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo hành trình sản phẩm mới
      tags:
      - Product Journeys
  /product-journeys/{id}:
    delete:
      consumes:
      - application/json
      description: Xóa một hành trình sản phẩm khỏi hệ thống
      parameters:
      - description: ID hành trình
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa hành trình sản phẩm
      tags:
      - Product Journeys
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một hành trình sản phẩm
      parameters:
      - description: ID hành trình
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.ProductJourneyResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin chi tiết hành trình sản phẩm
      tags:
      - Product Journeys
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một hành trình sản phẩm
      parameters:
      - description: ID hành trình
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin hành trình
        in: body
        name: journey
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.ProductJourneyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.ProductJourneyResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật hành trình sản phẩm
      tags:
      - Product Journeys
  /product-stages:
    get:
      consumes:
      - application/json
      description: Trả về danh sách tất cả giai đoạn sản phẩm trong hệ thống
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_models.ProductStage'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách tất cả giai đoạn sản phẩm
      tags:
      - Product-Stages
    post:
      consumes:
      - application/json
      description: Tạo một giai đoạn sản phẩm mới trong hệ thống
      parameters:
      - description: Thông tin giai đoạn sản phẩm
        in: body
        name: productStage
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.ProductStageRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/pvs-api_internal_models.ProductStage'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo giai đoạn sản phẩm mới
      tags:
      - Product-Stages
  /product-stages/{id}:
    delete:
      consumes:
      - application/json
      description: Xóa một giai đoạn sản phẩm khỏi hệ thống
      parameters:
      - description: ID giai đoạn sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa giai đoạn sản phẩm
      tags:
      - Product-Stages
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một giai đoạn sản phẩm
      parameters:
      - description: ID giai đoạn sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_models.ProductStage'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin giai đoạn sản phẩm theo ID
      tags:
      - Product-Stages
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một giai đoạn sản phẩm
      parameters:
      - description: ID giai đoạn sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin giai đoạn sản phẩm
        in: body
        name: productStage
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.ProductStageRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_models.ProductStage'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật thông tin giai đoạn sản phẩm
      tags:
      - Product-Stages
  /product-stages/{id}/stakeholders:
    get:
      consumes:
      - application/json
      description: Trả về danh sách các người phụ trách tham gia vào giai đoạn sản
        phẩm
      parameters:
      - description: ID giai đoạn sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/pvs-api_internal_models.ProductStageStakeholder'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách người phụ trách của giai đoạn sản phẩm
      tags:
      - Product-Stages
    post:
      consumes:
      - application/json
      description: Thêm một người phụ trách vào giai đoạn sản phẩm
      parameters:
      - description: ID giai đoạn sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin người phụ trách
        in: body
        name: stakeholder
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.ProductStageStakeholderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Thêm người phụ trách vào giai đoạn sản phẩm
      tags:
      - Product-Stages
  /product-stages/{id}/stakeholders/{stakeholder_id}:
    delete:
      consumes:
      - application/json
      description: Xóa một người phụ trách khỏi giai đoạn sản phẩm
      parameters:
      - description: ID giai đoạn sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: ID người phụ trách
        in: path
        name: stakeholder_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa người phụ trách khỏi giai đoạn sản phẩm
      tags:
      - Product-Stages
  /product-usage/all-products/stats:
    get:
      consumes:
      - application/json
      description: Lấy số liệu người dùng của tất cả sản phẩm trong khoảng thời gian
      parameters:
      - description: Tháng bắt đầu (YYYY-MM)
        in: query
        name: start_month
        required: true
        type: string
      - description: Tháng kết thúc (YYYY-MM)
        in: query
        name: end_month
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/internal_controllers.MultiProductStatsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy số liệu tất cả sản phẩm theo thời gian
      tags:
      - Product-Usage
  /product-usage/monthly-stats:
    get:
      consumes:
      - application/json
      description: Lấy số liệu người dùng của tất cả sản phẩm trong một tháng
      parameters:
      - description: Tháng (YYYY-MM)
        in: query
        name: month
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              allOf:
              - $ref: '#/definitions/pvs-api_pkg_utils.Response'
              - properties:
                  data:
                    items:
                      $ref: '#/definitions/pvs-api_internal_models.ProductUsageStat'
                    type: array
                type: object
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy số liệu người dùng theo tháng
      tags:
      - Product-Usage
  /product-usage/multi-products/stats:
    get:
      consumes:
      - application/json
      description: Lấy số liệu người dùng của nhiều sản phẩm trong khoảng thời gian
      parameters:
      - description: 'Danh sách ID sản phẩm, phân cách bởi dấu phẩy (vd: 1,2,3)'
        in: query
        name: product_ids
        required: true
        type: string
      - description: Ngày bắt đầu (YYYY-MM-DD)
        in: query
        name: start_date
        required: true
        type: string
      - description: Ngày kết thúc (YYYY-MM-DD)
        in: query
        name: end_date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/internal_controllers.MultiProductStatsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy số liệu nhiều sản phẩm theo thời gian
      tags:
      - Product-Usage
  /product-usage/products/{product_id}/stats:
    get:
      consumes:
      - application/json
      description: Lấy số liệu người dùng của một sản phẩm trong khoảng thời gian
      parameters:
      - description: ID sản phẩm
        in: path
        name: product_id
        required: true
        type: integer
      - description: Ngày bắt đầu (YYYY-MM-DD)
        in: query
        name: start_date
        required: true
        type: string
      - description: Ngày kết thúc (YYYY-MM-DD)
        in: query
        name: end_date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              allOf:
              - $ref: '#/definitions/pvs-api_pkg_utils.Response'
              - properties:
                  data:
                    items:
                      $ref: '#/definitions/pvs-api_internal_models.ProductUsageStat'
                    type: array
                type: object
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy số liệu người dùng theo sản phẩm
      tags:
      - Product-Usage
  /product-usage/stats:
    post:
      consumes:
      - application/json
      description: Cập nhật số lượng người dùng cho một sản phẩm trong tháng
      parameters:
      - description: Thông tin số liệu
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_models.ProductUsageStat'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_models.ProductUsageStat'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật số liệu người dùng sản phẩm
      tags:
      - Product-Usage
  /products:
    get:
      consumes:
      - application/json
      description: Trả về danh sách tất cả sản phẩm trong hệ thống
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_dto.ProductResponse'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách tất cả sản phẩm
      tags:
      - Products
    post:
      consumes:
      - application/json
      description: Tạo một sản phẩm mới với thông tin được cung cấp
      parameters:
      - description: Thông tin sản phẩm
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.ProductRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.ProductResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo sản phẩm mới
      tags:
      - Products
  /products/{id}:
    delete:
      consumes:
      - application/json
      description: Xóa một sản phẩm theo ID
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa sản phẩm
      tags:
      - Products
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một sản phẩm theo ID
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.ProductResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin một sản phẩm
      tags:
      - Products
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một sản phẩm theo ID
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin sản phẩm
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.ProductRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.ProductResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật sản phẩm
      tags:
      - Products
  /products/{id}/components:
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một sản phẩm và danh sách các thành
        phần hệ thống liên quan
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/pvs-api_internal_models.Component'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy sản phẩm và các thành phần liên quan
      tags:
      - Products
    post:
      consumes:
      - application/json
      description: Thêm một component vào sản phẩm sử dụng request body
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin component
        in: body
        name: component
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.AddComponentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Thêm component vào sản phẩm qua request body
      tags:
      - Products
  /products/{id}/components/{component_id}:
    delete:
      consumes:
      - application/json
      description: Xóa một thành phần hệ thống khỏi một sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: ID thành phần
        in: path
        name: component_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa thành phần khỏi sản phẩm
      tags:
      - Products
    post:
      consumes:
      - application/json
      description: Thêm một thành phần hệ thống vào một sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: ID thành phần
        in: path
        name: component_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Thêm thành phần vào sản phẩm
      tags:
      - Products
  /products/{id}/favorite:
    patch:
      consumes:
      - application/json
      description: Cập nhật trạng thái yêu thích (favorite) của một sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Trạng thái yêu thích (true/false)
        in: query
        name: is_favorite
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Toggle trạng thái yêu thích của sản phẩm
      tags:
      - Products
  /products/{id}/product-groups:
    get:
      consumes:
      - application/json
      description: Trả về danh sách các nhóm sản phẩm mà sản phẩm thuộc về
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.ProductDetailResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy các nhóm sản phẩm liên quan
      tags:
      - Products
  /products/{id}/product-groups/{group_id}:
    delete:
      consumes:
      - application/json
      description: Xóa một sản phẩm khỏi một nhóm sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: ID nhóm sản phẩm
        in: path
        name: group_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa sản phẩm khỏi nhóm sản phẩm
      tags:
      - Products
    post:
      consumes:
      - application/json
      description: Thêm một sản phẩm vào một nhóm sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: ID nhóm sản phẩm
        in: path
        name: group_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Thêm sản phẩm vào nhóm sản phẩm
      tags:
      - Products
  /products/{id}/set-stage:
    post:
      consumes:
      - application/json
      description: Thiết lập giai đoạn mới cho sản phẩm và cập nhật giai đoạn hiện
        tại của sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin giai đoạn mới
        in: body
        name: stage
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.SetProductStageRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_models.ProductStage'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Thiết lập giai đoạn mới cho sản phẩm
      tags:
      - Products
  /products/{id}/stage:
    get:
      consumes:
      - application/json
      description: Trả về giai đoạn hiện tại cùng của sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy giai đoạn hiện tại của sản phẩm
      tags:
      - Products
  /products/{id}/stages:
    get:
      consumes:
      - application/json
      description: Trả về lịch sử các giai đoạn của sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/pvs-api_internal_models.ProductStage'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin các giai đoạn
      tags:
      - Products
  /products/{id}/status:
    patch:
      consumes:
      - application/json
      description: Thay đổi trạng thái active/inactive của sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Trạng thái sản phẩm
        in: query
        name: status
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Thay đổi trạng thái sản phẩm
      tags:
      - Products
  /products/{id}/status-logs:
    get:
      consumes:
      - application/json
      description: Trả về lịch sử các thay đổi trạng thái của sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/pvs-api_internal_models.ProductStatusLog'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy lịch sử trạng thái sản phẩm
      tags:
      - Products
    post:
      consumes:
      - application/json
      description: Thêm một status log mới cho sản phẩm
      parameters:
      - description: ID sản phẩm
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin status log
        in: body
        name: status_log
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.AddStatusLogRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Thêm status log cho sản phẩm
      tags:
      - Products
  /products/grouped:
    get:
      consumes:
      - application/json
      description: Trả về danh sách các group và sản phẩm thuộc group đó, có thể lọc
        theo group, trạng thái và giai đoạn hiện tại
      parameters:
      - description: Số trang
        in: query
        name: page
        type: integer
      - description: Số lượng sản phẩm mỗi trang
        in: query
        name: limit
        type: integer
      - description: ID của group cần lọc
        in: query
        name: group_id
        type: integer
      - description: Năm cần lọc
        in: query
        name: year
        type: integer
      - description: Trạng thái cần lọc
        in: query
        name: status
        type: string
      - description: Lọc theo trạng thái yêu thích
        in: query
        name: is_favorite
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.GroupedProductsResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách sản phẩm được nhóm theo group có phân trang
      tags:
      - Products
  /products/search:
    get:
      consumes:
      - application/json
      description: Tìm kiếm sản phẩm theo tên và mô tả (tối thiểu 3 ký tự)
      parameters:
      - description: Từ khóa tìm kiếm (tối thiểu 3 ký tự)
        in: query
        name: q
        required: true
        type: string
      - description: 'Số lượng kết quả tối đa (mặc định: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_dto.ProductResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tìm kiếm sản phẩm
      tags:
      - Products
  /refresh-token:
    post:
      consumes:
      - application/json
      description: Tạo token mới dựa trên token hiện tại
      parameters:
      - description: Token hiện tại
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Token mới
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.AuthResponse'
              type: object
        "400":
          description: Lỗi dữ liệu đầu vào
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Token không hợp lệ hoặc người dùng không tồn tại
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Lỗi server
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      summary: Làm mới token
      tags:
      - Authentication
  /roles:
    get:
      consumes:
      - application/json
      description: Trả về danh sách tất cả vai trò có phân trang
      parameters:
      - description: Số trang
        in: query
        name: page
        type: integer
      - description: Số lượng mỗi trang
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_dto.RoleResponse'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách vai trò
      tags:
      - Roles
    post:
      consumes:
      - application/json
      description: Tạo một vai trò mới với thông tin được cung cấp
      parameters:
      - description: Thông tin vai trò
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.RoleRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/pvs-api_internal_dto.RoleResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo vai trò mới
      tags:
      - Roles
  /roles/{code}:
    delete:
      consumes:
      - application/json
      description: Xóa một vai trò theo mã
      parameters:
      - description: Mã vai trò
        in: path
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request - Role is assigned to users
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "403":
          description: Forbidden - Cannot delete system role
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa vai trò
      tags:
      - Roles
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một vai trò theo mã
      parameters:
      - description: Mã vai trò
        in: path
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_dto.RoleResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin vai trò theo mã
      tags:
      - Roles
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một vai trò theo mã
      parameters:
      - description: Mã vai trò
        in: path
        name: code
        required: true
        type: string
      - description: Thông tin vai trò
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.RoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_dto.RoleResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "403":
          description: Forbidden - Cannot modify system role
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật thông tin vai trò
      tags:
      - Roles
  /roles/{code}/permissions:
    patch:
      consumes:
      - application/json
      description: Cập nhật danh sách quyền cho một vai trò theo mã
      parameters:
      - description: Mã vai trò
        in: path
        name: code
        required: true
        type: string
      - description: Danh sách quyền mới
        in: body
        name: permissions
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.UpdateRolePermissionsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "403":
          description: Forbidden - Cannot modify system role
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật quyền cho vai trò
      tags:
      - Roles
  /stakeholders:
    get:
      consumes:
      - application/json
      description: Trả về danh sách tất cả người phụ trách trong hệ thống
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách tất cả người phụ trách
      tags:
      - Stakeholders
    post:
      consumes:
      - application/json
      description: Tạo một người phụ trách mới trong hệ thống
      parameters:
      - description: Thông tin người phụ trách
        in: body
        name: stakeholder
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.StakeholderRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/pvs-api_internal_dto.StakeholderResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo người phụ trách mới
      tags:
      - Stakeholders
  /stakeholders/{id}:
    delete:
      consumes:
      - application/json
      description: Xóa một người phụ trách khỏi hệ thống
      parameters:
      - description: ID người phụ trách
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa người phụ trách
      tags:
      - Stakeholders
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một người phụ trách
      parameters:
      - description: ID người phụ trách
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_dto.StakeholderResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin người phụ trách theo ID
      tags:
      - Stakeholders
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một người phụ trách
      parameters:
      - description: ID người phụ trách
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin người phụ trách
        in: body
        name: stakeholder
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.StakeholderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_internal_dto.StakeholderResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật thông tin người phụ trách
      tags:
      - Stakeholders
  /system-params:
    get:
      consumes:
      - application/json
      description: Trả về danh sách tham số hệ thống có phân trang
      parameters:
      - description: Số trang
        in: query
        name: page
        type: integer
      - description: Số lượng mỗi trang
        in: query
        name: limit
        type: integer
      - description: Lọc theo loại tham số
        in: query
        name: type
        type: string
      - description: Lọc theo trạng thái kích hoạt
        in: query
        name: is_active
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/pvs-api_internal_dto.SystemParamResponse'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách tham số hệ thống
      tags:
      - System-Params
    post:
      consumes:
      - application/json
      description: Tạo một tham số hệ thống mới với thông tin được cung cấp
      parameters:
      - description: Thông tin tham số hệ thống
        in: body
        name: systemParam
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.SystemParamRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.SystemParamResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo tham số hệ thống mới
      tags:
      - System-Params
  /system-params/{id}:
    delete:
      consumes:
      - application/json
      description: Xóa một tham số hệ thống khỏi hệ thống
      parameters:
      - description: ID tham số hệ thống
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Xóa tham số hệ thống
      tags:
      - System-Params
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một tham số hệ thống
      parameters:
      - description: ID tham số hệ thống
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.SystemParamResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin tham số hệ thống theo ID
      tags:
      - System-Params
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một tham số hệ thống
      parameters:
      - description: ID tham số hệ thống
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin tham số hệ thống
        in: body
        name: systemParam
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.SystemParamRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.SystemParamResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật tham số hệ thống
      tags:
      - System-Params
  /system-params/types:
    get:
      consumes:
      - application/json
      description: Trả về danh sách tất cả các loại tham số hệ thống
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách tất cả loại tham số
      tags:
      - System-Params
  /timeline:
    get:
      consumes:
      - application/json
      description: Returns a timeline view of all product groups with their products,
        stages, status logs, and components
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_dto.TimelineResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Get product timeline
      tags:
      - Timeline
  /users:
    get:
      consumes:
      - application/json
      description: Trả về danh sách người dùng có phân trang
      parameters:
      - description: Số trang
        in: query
        name: page
        type: integer
      - description: Số lượng mỗi trang
        in: query
        name: limit
        type: integer
      - description: Lọc theo trạng thái kích hoạt
        in: query
        name: is_active
        type: boolean
      - description: Lọc theo role code
        in: query
        name: role_code
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy danh sách người dùng
      tags:
      - Users
    post:
      consumes:
      - application/json
      description: Tạo một người dùng mới trong hệ thống
      parameters:
      - description: Thông tin người dùng
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.CreateUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_models.User'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Tạo người dùng mới
      tags:
      - Users
  /users/{id}:
    get:
      consumes:
      - application/json
      description: Trả về thông tin chi tiết của một người dùng
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_models.User'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Lấy thông tin người dùng theo ID
      tags:
      - Users
    put:
      consumes:
      - application/json
      description: Cập nhật thông tin của một người dùng
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin người dùng
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/pvs-api_pkg_utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/pvs-api_internal_models.User'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật thông tin người dùng
      tags:
      - Users
  /users/{id}/password:
    patch:
      consumes:
      - application/json
      description: Cập nhật mật khẩu của một người dùng
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin mật khẩu
        in: body
        name: passwords
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Đổi mật khẩu người dùng
      tags:
      - Users
  /users/{id}/role:
    patch:
      consumes:
      - application/json
      description: Thay đổi role của một người dùng
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Thông tin role
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/pvs-api_internal_dto.UpdateUserRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/pvs-api_pkg_utils.Response'
      security:
      - BearerAuth: []
      summary: Cập nhật role của người dùng
      tags:
      - Users
securityDefinitions:
  BearerAuth:
    description: 'Xác thực bằng JWT Token với tiền tố Bearer. Ví dụ: "Bearer {token}"'
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"

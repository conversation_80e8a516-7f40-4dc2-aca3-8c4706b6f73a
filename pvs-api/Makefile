# Application commands
run:
	go run cmd/main.go

build:
	go build -o bin/app cmd/main.go

# Migration commands
migrate-build:
	go build -o bin/migrate cmd/migrate/main.go

migrate-auto: migrate-build
	./bin/migrate -auto

seed: migrate-build
	./bin/migrate -seed

db-cleanup: migrate-build
	./bin/migrate -cleanup

db-cleanup-force: migrate-build
	./bin/migrate -cleanup -force

db-reset: db-cleanup-force migrate-auto migrate-seed
	@echo "Database has been reset, migrated, and seeded successfully."

# Documentation commands
swag-init:
	swag init -g cmd/main.go -o ./docs --parseDependency --parseInternal --parseDepth 1

# Docker commands
docker-build:
	docker build -t pvs-api .

docker-run:
	docker run -p 8080:8080 --env-file .env pvs-api

package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"

	"gorm.io/gorm"
)

// SeedNotableSystems tạo dữ liệu ban đầu cho bảng NotableSystem
func SeedNotableSystems(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.NotableSystem{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("NotableSystem table already has data, skipping seed")
		return nil
	}

	// Tạo notable systems theo dữ liệu thực tế
	notableSystems := []*models.NotableSystem{
		{
			ID:          1,
			Name:        "BOSC",
			Description: "BOSC",
			StartYear:   2006,
			EndYear:     &[]int{2009}[0],
		},
		{
			ID:          2,
			Name:        "Freewill",
			Description: "Freewill IFIS",
			StartYear:   2010,
			EndYear:     &[]int{2012}[0],
		},
		{
			ID:          3,
			Name:        "Innotech",
			Description: "Innotech VPS Portal",
			StartYear:   2013,
			EndYear:     &[]int{2017}[0],
		},
		{
			ID:          4,
			Name:        "Nowaday",
			Description: "01/03 Webtrade\n14/09 SMO APP\n22/10 Core NY",
			StartYear:   2018,
			EndYear:     nil, // NULL value
		},
	}

	// Tạo dữ liệu trong transaction
	return CreateInTransaction(ctx, db, &notableSystems, "NotableSystem")
}

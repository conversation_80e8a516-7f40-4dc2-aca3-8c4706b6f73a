package migrations

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"pvs-api/internal/models"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// SeedProductComponentsFromCSV tạo dữ liệu ban đầu cho bảng ProductComponent từ CSV
func SeedProductComponentsFromCSV(db *gorm.DB) error {
	ctx := context.Background()

	// Xóa dữ liệu cũ trước khi seed
	if err := db.Exec("DELETE FROM product_components").Error; err != nil {
		return fmt.Errorf("failed to delete existing product components: %w", err)
	}
	fmt.Println("Existing product components deleted, proceeding with seed")

	// Đọc file CSV
	csvFile, err := os.Open(filepath.Join("data", "product_components.csv"))
	if err != nil {
		return fmt.Errorf("failed to open product_components.csv: %w", err)
	}
	defer csvFile.Close()

	reader := csv.NewReader(csvFile)
	reader.Comma = ';' // Set delimiter to semicolon
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to read CSV: %w", err)
	}

	// Bỏ qua header row
	if len(records) <= 1 {
		return fmt.Errorf("CSV file is empty or contains only headers")
	}

	productComponents := make([]*models.ProductComponent, 0, len(records)-1)

	// Xử lý từng record, bắt đầu từ row thứ 2 (index 1)
	for i, record := range records[1:] {
		if len(record) < 2 { // Kiểm tra đủ số cột (product_id, component_id)
			return fmt.Errorf("invalid record at row %d: expected 2 columns, got %d", i+2, len(record))
		}

		// Parse Product ID
		productID, err := strconv.ParseUint(strings.TrimSpace(record[0]), 10, 32)
		if err != nil {
			return fmt.Errorf("invalid Product ID at row %d: %w", i+2, err)
		}

		// Parse Component ID
		componentID, err := strconv.ParseUint(strings.TrimSpace(record[1]), 10, 32)
		if err != nil {
			return fmt.Errorf("invalid Component ID at row %d: %w", i+2, err)
		}

		productComponent := &models.ProductComponent{
			ProductID:   uint(productID),
			ComponentID: uint(componentID),
		}

		productComponents = append(productComponents, productComponent)
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, pc := range productComponents {
			if err := tx.Create(pc).Error; err != nil {
				return fmt.Errorf("failed to create product-component relationship (Product: %d, Component: %d): %w",
					pc.ProductID, pc.ComponentID, err)
			}
		}

		fmt.Printf("ProductComponent data seeded successfully (%d records)\n", len(productComponents))
		return nil
	})
}

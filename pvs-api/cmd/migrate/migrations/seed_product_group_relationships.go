package migrations

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"pvs-api/internal/models"
	"strconv"

	"gorm.io/gorm"
)

// SeedProductGroupRelationships tạo relationships giữa products và groups từ CSV
func SeedProductGroupRelationships(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có relationships chưa
	var count int64
	if err := db.Model(&models.ProductProductGroup{}).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check product-group relationships: %w", err)
	}

	if count > 0 {
		fmt.Println("Product-group relationships already exist, skipping seed")
		return nil
	}

	// Đọc file CSV
	csvFile, err := os.Open(filepath.Join("data", "product_groups_relationships.csv"))
	if err != nil {
		return fmt.Errorf("failed to open product_groups_relationships.csv: %w", err)
	}
	defer csvFile.Close()

	reader := csv.NewReader(csvFile)
	reader.LazyQuotes = true
	reader.TrimLeadingSpace = true
	reader.FieldsPerRecord = -1
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to read CSV: %w", err)
	}

	// Bỏ qua header row
	if len(records) < 2 {
		return fmt.Errorf("CSV file is empty or has no data rows")
	}

	var relationships []*models.ProductProductGroup

	// Xử lý từng dòng dữ liệu (bỏ qua header)
	for i, record := range records[1:] {
		if len(record) < 2 {
			fmt.Printf("Warning: Invalid CSV format at row %d: expected 2 fields, got %d\n", i+2, len(record))
			continue
		}

		// Parse product ID
		productID, err := strconv.ParseUint(record[0], 10, 32)
		if err != nil {
			fmt.Printf("Warning: Invalid product ID at row %d: %v\n", i+2, err)
			continue
		}

		// Parse group ID
		groupID, err := strconv.ParseUint(record[1], 10, 32)
		if err != nil {
			fmt.Printf("Warning: Invalid group ID at row %d: %v\n", i+2, err)
			continue
		}

		relationship := &models.ProductProductGroup{
			ProductID:      uint(productID),
			ProductGroupID: uint(groupID),
		}

		relationships = append(relationships, relationship)
	}

	// Tạo relationships trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, rel := range relationships {
			if err := tx.Create(rel).Error; err != nil {
				fmt.Printf("Warning: Failed to create product-group relationship for product %d and group %d: %v\n", 
					rel.ProductID, rel.ProductGroupID, err)
				continue
			}
		}
		fmt.Printf("Product-group relationships seeded successfully (%d records)\n", len(relationships))
		return nil
	})
}

package migrations

import (
	"fmt"

	"gorm.io/gorm"
)

// SeedAll thực hiện tất cả các seed functions
func SeedAll(db *gorm.DB) error {
	// Seed SystemParams
	if err := SeedSystemParams(db); err != nil {
		return fmt.Errorf("failed to seed system params: %w", err)
	}

	// Seed Roles
	if err := SeedRoles(db); err != nil {
		return fmt.Errorf("failed to seed roles: %w", err)
	}

	// Seed Users
	if err := SeedUsers(db); err != nil {
		return fmt.Errorf("failed to seed users: %w", err)
	}

	// Seed Teams
	if err := SeedTeams(db); err != nil {
		return fmt.Errorf("failed to seed teams: %w", err)
	}

	// Seed Product Groups
	if err := SeedProductGroups(db); err != nil {
		return fmt.Errorf("failed to seed product groups: %w", err)
	}

	// Seed Brand Identities
	if err := SeedBrandIdentities(db); err != nil {
		return fmt.Errorf("failed to seed brand identities: %w", err)
	}

	// Seed Notable Systems
	if err := SeedNotableSystems(db); err != nil {
		return fmt.Errorf("failed to seed notable systems: %w", err)
	}

	// Seed Components từ CSV
	if err := SeedComponentsFromCSV(db); err != nil {
		return fmt.Errorf("failed to seed components from CSV: %w", err)
	}

	// Seed Products từ CSV
	if err := SeedProductsFromCSV(db); err != nil {
		return fmt.Errorf("failed to seed products from CSV: %w", err)
	}

	// Seed Product Stages từ CSV
	if err := SeedProductStagesFromCSV(db); err != nil {
		return fmt.Errorf("failed to seed product stages from CSV: %w", err)
	}

	// Seed Product-Group relationships từ CSV
	if err := SeedProductGroupRelationships(db); err != nil {
		return fmt.Errorf("failed to seed product-group relationships from CSV: %w", err)
	}

	// Seed Product Components từ CSV
	if err := SeedProductComponentsFromCSV(db); err != nil {
		return fmt.Errorf("failed to seed product components from CSV: %w", err)
	}

	// Seed Product Status Logs từ CSV
	if err := SeedProductStatusLogsFromCSV(db); err != nil {
		return fmt.Errorf("failed to seed product status logs from CSV: %w", err)
	}

	// Thêm seed cho ProductUsage
	if err := SeedProductUsage(db); err != nil {
		return fmt.Errorf("failed to seed product usage stats: %w", err)
	}

	fmt.Println("All seed data created successfully")
	return nil
}

package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"
	"time"

	"gorm.io/gorm"
)

// SeedBrandIdentities tạo dữ liệu ban đầu cho bảng BrandIdentity
func SeedBrandIdentities(db *gorm.DB) error {
	ctx := context.Background()

	// Xóa dữ liệu cũ trước khi seed
	if err := db.Exec("DELETE FROM brand_identities").Error; err != nil {
		return fmt.Errorf("failed to delete existing brand identities: %w", err)
	}
	fmt.Println("Existing brand identities deleted, proceeding with seed")

	// Parse dates
	parseDate := func(dateStr string) time.Time {
		t, _ := time.Parse("2006-01-02", dateStr)
		return t
	}

	// Tạo brand identity theo dữ liệu thực tế
	brandIdentities := []*models.BrandIdentity{
		{
			ID:          1,
			CompanyName: "CT TNHH Chứng khoán ngân hàng TMCP các DN ngoài quốc doanh",
			ColorCode:   "#1EEEEE",
			LogoURL:     "",
			EffectiveAt: parseDate("2006-09-29"),
			ExpiredAt:   &[]time.Time{parseDate("2010-09-26")}[0],
		},
		{
			ID:          2,
			CompanyName: "CT TNHH chứng khoán Ngân hàng TMCP Việt Nam Thịnh Vượng",
			ColorCode:   "#008346",
			LogoURL:     "/assets/logo_vpbs.png",
			EffectiveAt: parseDate("2010-09-27"),
			ExpiredAt:   &[]time.Time{parseDate("2019-02-20")}[0],
		},
		{
			ID:          3,
			CompanyName: "Công ty Cổ phần chứng khoán VPS",
			ColorCode:   "#ED1C24",
			LogoURL:     "/assets/logo_vps1.png",
			EffectiveAt: parseDate("2019-02-21"),
			ExpiredAt:   &[]time.Time{parseDate("2024-12-13")}[0],
		},
		{
			ID:          4,
			CompanyName: "Công ty Cổ phần chứng khoán VPS",
			ColorCode:   "#8229E3",
			LogoURL:     "/assets/logo_vps2.png",
			EffectiveAt: parseDate("2024-12-14"),
			ExpiredAt:   nil, // NULL value
		},
	}

	// Tạo dữ liệu trong transaction
	return CreateInTransaction(ctx, db, &brandIdentities, "BrandIdentity")
}

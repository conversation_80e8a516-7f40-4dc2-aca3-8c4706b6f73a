package migrations

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"pvs-api/internal/models"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// SeedProductStagesFromCSV tạo dữ liệu ban đầu cho bảng ProductStage từ CSV
func SeedProductStagesFromCSV(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.ProductStage{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("ProductStage table already has data, skipping seed")
		return nil
	}

	// Đọc file CSV
	csvFile, err := os.Open(filepath.Join("data", "product_stages.csv"))
	if err != nil {
		return fmt.Errorf("failed to open product_stages.csv: %w", err)
	}
	defer csvFile.Close()

	reader := csv.NewReader(csvFile)
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to read CSV: %w", err)
	}

	// Bỏ qua header row
	if len(records) < 2 {
		return fmt.Errorf("CSV file is empty or has no data rows")
	}

	var productStages []*models.ProductStage

	// Xử lý từng dòng dữ liệu (bỏ qua header)
	for i, record := range records[1:] {
		if len(record) < 4 {
			return fmt.Errorf("invalid CSV format at row %d", i+2)
		}

		// Parse ID
		id, err := strconv.ParseUint(record[0], 10, 32)
		if err != nil {
			return fmt.Errorf("invalid ID at row %d: %w", i+2, err)
		}

		// Parse ProductID
		productID, err := strconv.ParseUint(record[1], 10, 32)
		if err != nil {
			return fmt.Errorf("invalid product_id at row %d: %w", i+2, err)
		}

		// Parse StartDate
		startDate, err := time.Parse("2006-01-02", record[3])
		if err != nil {
			return fmt.Errorf("invalid start_date at row %d: %w", i+2, err)
		}

		// Parse EndDate (optional)
		var endDate *time.Time
		if len(record) > 4 && record[4] != "" {
			parsedEndDate, err := time.Parse("2006-01-02", record[4])
			if err != nil {
				return fmt.Errorf("invalid end_date at row %d: %w", i+2, err)
			}
			endDate = &parsedEndDate
		}

		productStage := &models.ProductStage{
			ID:        uint(id),
			ProductID: uint(productID),
			StageCode: record[2],
			StartDate: startDate,
			EndDate:   endDate,
		}

		productStages = append(productStages, productStage)
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, productStage := range productStages {
			if err := tx.Create(productStage).Error; err != nil {
				return fmt.Errorf("failed to create product stage for product %d: %w", productStage.ProductID, err)
			}
		}
		fmt.Printf("ProductStage data seeded successfully (%d records)\n", len(productStages))

		// Update products with their latest stage_id
		return updateProductLatestStages(tx)
	})
}

// updateProductLatestStages updates each product with its latest stage_id
func updateProductLatestStages(db *gorm.DB) error {
	// Get all products
	var products []models.Product
	if err := db.Find(&products).Error; err != nil {
		return fmt.Errorf("failed to get products: %w", err)
	}

	for _, product := range products {
		// Find the latest stage for this product (highest ID or latest start date)
		var latestStage models.ProductStage
		err := db.Where("product_id = ?", product.ID).
			Order("start_date DESC, id DESC").
			First(&latestStage).Error

		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// No stages for this product, skip
				continue
			}
			return fmt.Errorf("failed to get latest stage for product %d: %w", product.ID, err)
		}

		// Update product with latest stage_id
		if err := db.Model(&product).Update("stage_id", latestStage.ID).Error; err != nil {
			return fmt.Errorf("failed to update product %d with stage_id %d: %w", product.ID, latestStage.ID, err)
		}
	}

	fmt.Println("Product stage references updated successfully")
	return nil
}

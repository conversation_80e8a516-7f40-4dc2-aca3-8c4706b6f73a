package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"

	"gorm.io/gorm"
)

// SeedUsers tạo dữ liệu ban đầu cho bảng User
func SeedUsers(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.User{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("User table already has data, skipping seed")
		return nil
	}

	// Tạo user admin mặc định
	adminUser := &models.User{
		Username: "admin",
		Password: "Admin@123", // Sẽ được hash tự động bởi BeforeSave hook
		FullName: "System Administrator",
		RoleCode: "ADMIN",
		IsActive: true,
	}

	userManUser := &models.User{
		Username: "userman",
		Password: "Admin@123", // Sẽ được hash tự động bởi BeforeSave hook
		FullName: "User Manager",
		RoleCode: "USER_MANAGER",
		IsActive: true,
	}

	productManUser := &models.User{
		Username: "productman",
		Password: "Admin@123", // Sẽ được hash tự động bởi BeforeSave hook
		FullName: "Product Manager",
		RoleCode: "PRODUCT_MANAGER",
		IsActive: true,
	}

	componentManUser := &models.User{
		Username: "componentman",
		Password: "Admin@123", // Sẽ được hash tự động bởi BeforeSave hook
		FullName: "Component Manager",
		RoleCode: "COMPONENT_MANAGER",
		IsActive: true,
	}

	viewerUser := &models.User{
		Username: "viewer",
		Password: "Admin@123", // Sẽ được hash tự động bởi BeforeSave hook
		FullName: "Viewer",
		RoleCode: "VIEWER",
		IsActive: true,
	}

	users := []*models.User{adminUser, userManUser, productManUser, componentManUser, viewerUser}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, user := range users {
			if err := tx.Create(user).Error; err != nil {
				return fmt.Errorf("failed to create user %s: %w", user.Username, err)
			}
		}

		fmt.Println("Admin user created successfully")
		return nil
	})
}

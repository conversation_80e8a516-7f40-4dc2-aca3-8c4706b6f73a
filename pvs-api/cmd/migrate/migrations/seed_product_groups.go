package migrations

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"pvs-api/internal/models"
	"strconv"

	"gorm.io/gorm"
)

// SeedProductGroups tạo dữ liệu ban đầu cho bảng ProductGroup từ CSV
func SeedProductGroups(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.ProductGroup{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("ProductGroup table already has data, skipping seed")
		return nil
	}

	// Đọc file CSV
	csvFile, err := os.Open(filepath.Join("data", "product_groups.csv"))
	if err != nil {
		return fmt.Errorf("failed to open product_groups.csv: %w", err)
	}
	defer csvFile.Close()

	reader := csv.NewReader(csvFile)
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to read CSV: %w", err)
	}

	// Bỏ qua header row
	if len(records) < 2 {
		return fmt.Errorf("CSV file is empty or has no data rows")
	}

	var productGroups []*models.ProductGroup

	// Xử lý từng dòng dữ liệu (bỏ qua header)
	for i, record := range records[1:] {
		if len(record) < 4 {
			return fmt.Errorf("invalid CSV format at row %d", i+2)
		}

		// Parse ID
		id, err := strconv.ParseUint(record[0], 10, 32)
		if err != nil {
			return fmt.Errorf("invalid ID at row %d: %w", i+2, err)
		}

		// Parse is_favourite
		isFavourite, err := strconv.ParseBool(record[3])
		if err != nil {
			return fmt.Errorf("invalid is_favourite at row %d: %w", i+2, err)
		}

		productGroup := &models.ProductGroup{
			ID:          uint(id),
			Name:        record[1],
			Description: record[2],
			IsFavorite:  isFavourite,
		}

		productGroups = append(productGroups, productGroup)
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, productGroup := range productGroups {
			if err := tx.Create(productGroup).Error; err != nil {
				return fmt.Errorf("failed to create product group %s: %w", productGroup.Name, err)
			}
		}
		fmt.Printf("ProductGroup data seeded successfully (%d records)\n", len(productGroups))
		return nil
	})
}

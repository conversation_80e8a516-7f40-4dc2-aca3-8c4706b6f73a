package migrations

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"pvs-api/internal/models"
	"strconv"

	"gorm.io/gorm"
)

// SeedProductsFromCSV tạo dữ liệu ban đầu cho bảng Product từ CSV
func SeedProductsFromCSV(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.Product{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("Product table already has data, skipping seed")
		return nil
	}

	// Đọc file CSV
	csvFile, err := os.Open(filepath.Join("data", "products.csv"))
	if err != nil {
		return fmt.Errorf("failed to open products.csv: %w", err)
	}
	defer csvFile.Close()

	reader := csv.NewReader(csvFile)
	reader.LazyQuotes = true // Allow lazy quotes to handle malformed CSV
	reader.TrimLeadingSpace = true
	reader.FieldsPerRecord = -1 // Allow variable number of fields
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to read CSV: %w", err)
	}

	// Bỏ qua header row
	if len(records) < 2 {
		return fmt.Errorf("CSV file is empty or has no data rows")
	}

	var products []*models.Product

	// Xử lý từng dòng dữ liệu (bỏ qua header)
	for i, record := range records[1:] {
		if len(record) < 4 {
			return fmt.Errorf("invalid CSV format at row %d: expected at least 4 fields, got %d", i+2, len(record))
		}

		// Parse ID
		id, err := strconv.ParseUint(record[0], 10, 32)
		if err != nil {
			return fmt.Errorf("invalid ID at row %d: %w", i+2, err)
		}

		// Don't set stage_id during initial creation to avoid circular dependency
		// The stage_id will be updated later after product stages are created
		product := &models.Product{
			ID:          uint(id),
			Name:        record[1],
			Description: record[2],
			Status:      record[3],
			StageID:     nil, // Will be updated later
		}

		products = append(products, product)
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, product := range products {
			if err := tx.Create(product).Error; err != nil {
				return fmt.Errorf("failed to create product %s: %w", product.Name, err)
			}
		}
		fmt.Printf("Product data seeded successfully (%d records)\n", len(products))
		return nil
	})
}

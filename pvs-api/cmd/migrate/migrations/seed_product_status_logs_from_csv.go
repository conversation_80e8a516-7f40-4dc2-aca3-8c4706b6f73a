package migrations

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"pvs-api/internal/models"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

// SeedProductStatusLogsFromCSV tạo dữ liệu ban đầu cho bảng ProductStatusLog từ CSV
func SeedProductStatusLogsFromCSV(db *gorm.DB) error {
	ctx := context.Background()

	// Xóa dữ liệu cũ trước khi seed
	if err := db.Exec("DELETE FROM product_status_logs").Error; err != nil {
		return fmt.Errorf("failed to delete existing product status logs: %w", err)
	}
	fmt.Println("Existing product status logs deleted, proceeding with seed")

	// Đọc file CSV
	csvFile, err := os.Open(filepath.Join("data", "product_status_logs.csv"))
	if err != nil {
		return fmt.Errorf("failed to open product_status_logs.csv: %w", err)
	}
	defer csvFile.Close()

	reader := csv.NewReader(csvFile)
	reader.Comma = ';' // Set delimiter to semicolon
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to read CSV: %w", err)
	}

	// Bỏ qua header row
	if len(records) <= 1 {
		return fmt.Errorf("CSV file is empty or contains only headers")
	}

	productStatusLogs := make([]*models.ProductStatusLog, 0, len(records)-1)

	// Xử lý từng record, bắt đầu từ row thứ 2 (index 1)
	for i, record := range records[1:] {
		if len(record) < 6 { // Kiểm tra đủ số cột (id, product_id, status, changed_by, changed_at, note)
			return fmt.Errorf("invalid record at row %d: expected 6 columns, got %d", i+2, len(record))
		}

		// Parse ID
		id, err := strconv.ParseUint(strings.TrimSpace(record[0]), 10, 32)
		if err != nil {
			return fmt.Errorf("invalid ID at row %d: %w", i+2, err)
		}

		// Parse Product ID
		productID, err := strconv.ParseUint(strings.TrimSpace(record[1]), 10, 32)
		if err != nil {
			return fmt.Errorf("invalid Product ID at row %d: %w", i+2, err)
		}

		// Parse Status
		status := strings.TrimSpace(record[2])
		if status == "" {
			return fmt.Errorf("empty status at row %d", i+2)
		}

		// Parse Changed By
		changedBy := strings.TrimSpace(record[3])

		// Parse Changed At (format: YYYY-MM-DD)
		changedAtStr := strings.TrimSpace(record[4])
		changedAt, err := time.Parse("2006-01-02", changedAtStr)
		if err != nil {
			return fmt.Errorf("invalid date format at row %d: %w", i+2, err)
		}

		// Parse Note
		note := strings.TrimSpace(record[5])

		productStatusLog := &models.ProductStatusLog{
			ID:        uint(id),
			ProductID: uint(productID),
			Status:    status,
			ChangedBy: changedBy,
			ChangedAt: changedAt,
			Note:      note,
		}

		productStatusLogs = append(productStatusLogs, productStatusLog)
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, psl := range productStatusLogs {
			if err := tx.Create(psl).Error; err != nil {
				return fmt.Errorf("failed to create product status log (ID: %d, Product: %d): %w",
					psl.ID, psl.ProductID, err)
			}
		}

		fmt.Printf("ProductStatusLog data seeded successfully (%d records)\n", len(productStatusLogs))
		return nil
	})
}

package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"

	"gorm.io/gorm"
)

// SeedTeams tạo dữ liệu ban đầu cho bảng Team
func SeedTeams(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.Team{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("Team table already has data, skipping seed")
		return nil
	}

	// Tạo teams từ dữ liệu trong hình
	teams := []*models.Team{
		{ID: 110, Name: "Team Mobi"},
		{ID: 111, Name: "Team Desktop & Web"},
		{ID: 112, Name: "Team FO"},
		{ID: 113, Name: "Team Cấu trúc"},
		{ID: 114, Name: "Team BGW"},
		{ID: 115, Name: "Team BO"},
		{ID: 116, Name: "Team Ứng dụng"},
		{ID: 117, Name: "Team Xử lý dữ liệu"},
		{ID: 118, Name: "Team AI"},
		{ID: 119, Name: "Team Blockchain"},
		{ID: 120, Name: "Team Tester"},
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, team := range teams {
			if err := tx.Create(team).Error; err != nil {
				return fmt.Errorf("failed to create team %s: %w", team.Name, err)
			}
		}
		fmt.Println("Team data seeded successfully")
		return nil
	})
}

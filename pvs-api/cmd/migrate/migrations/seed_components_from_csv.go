package migrations

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"pvs-api/internal/models"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// SeedComponentsFromCSV tạo dữ liệu ban đầu cho bảng Component từ CSV
func SeedComponentsFromCSV(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.Component{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("Component table already has data, skipping seed")
		return nil
	}

	// Đọc file CSV
	csvFile, err := os.Open(filepath.Join("data", "components.csv"))
	if err != nil {
		return fmt.Errorf("failed to open components.csv: %w", err)
	}
	defer csvFile.Close()

	reader := csv.NewReader(csvFile)
	reader.Comma = ';' // Set delimiter to semicolon
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to read CSV: %w", err)
	}

	// Bỏ qua header row
	if len(records) <= 1 {
		return fmt.Errorf("CSV file is empty or contains only headers")
	}

	components := make([]*models.Component, 0, len(records)-1)

	// Xử lý từng record, bắt đầu từ row thứ 2 (index 1)
	for i, record := range records[1:] {
		if len(record) < 9 { // Kiểm tra đủ số cột (id, name, description, importance, main_function, extra_function, component_type_code, target_user_code, confluence_url)
			return fmt.Errorf("invalid record at row %d: expected 9 columns, got %d", i+2, len(record))
		}

		// Parse ID
		id, err := strconv.ParseUint(record[0], 10, 32)
		if err != nil {
			return fmt.Errorf("invalid ID at row %d: %w", i+2, err)
		}

		// Chuẩn hóa ComponentType và TargetUser codes
		componentTypeCode := normalizeCode(record[6]) // component_type_code is at index 6
		targetUserCode := normalizeCode(record[7])    // target_user_code is at index 7

		component := &models.Component{
			ID:                uint(id),
			Name:              strings.TrimSpace(record[1]),
			Description:       strings.TrimSpace(record[2]),
			Importance:        strings.TrimSpace(record[3]),
			MainFunction:      strings.TrimSpace(record[4]),
			ExtraFunction:     strings.TrimSpace(record[5]),
			ComponentTypeCode: componentTypeCode,
			TargetUserCode:    targetUserCode,
			ConfluenceURL:     strings.TrimSpace(record[8]),
		}

		components = append(components, component)
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, component := range components {
			if err := tx.Create(component).Error; err != nil {
				return fmt.Errorf("failed to create component %s: %w", component.Name, err)
			}
		}

		fmt.Printf("Component data seeded successfully (%d records)\n", len(components))
		return nil
	})
}

// normalizeCode chuẩn hóa mã code
func normalizeCode(input string) string {
	// Chuyển đổi thành chữ hoa và thay thế khoảng trắng bằng dấu gạch dưới
	code := strings.ToUpper(strings.TrimSpace(input))
	code = strings.ReplaceAll(code, " ", "_")
	return code
}
